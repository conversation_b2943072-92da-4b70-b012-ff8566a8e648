"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_features_planificacion_services_planEstudiosService_ts";
exports.ids = ["_rsc_src_features_planificacion_services_planEstudiosService_ts"];
exports.modules = {

/***/ "(rsc)/./src/features/planificacion/services/planEstudiosService.ts":
/*!********************************************************************!*\
  !*** ./src/features/planificacion/services/planEstudiosService.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarPlan: () => (/* binding */ activarPlan),\n/* harmony export */   actualizarNotasPlan: () => (/* binding */ actualizarNotasPlan),\n/* harmony export */   eliminarPlan: () => (/* binding */ eliminarPlan),\n/* harmony export */   guardarPlanEstudios: () => (/* binding */ guardarPlanEstudios),\n/* harmony export */   guardarPlanEstudiosServidor: () => (/* binding */ guardarPlanEstudiosServidor),\n/* harmony export */   guardarProgresoTarea: () => (/* binding */ guardarProgresoTarea),\n/* harmony export */   obtenerEstadisticasProgreso: () => (/* binding */ obtenerEstadisticasProgreso),\n/* harmony export */   obtenerHistorialPlanes: () => (/* binding */ obtenerHistorialPlanes),\n/* harmony export */   obtenerProgresoPlan: () => (/* binding */ obtenerProgresoPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(rsc)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Guarda un plan de estudios generado en la base de datos (versión para cliente)\n */ async function guardarPlanEstudios(temarioId, planData, titulo) {\n    try {\n        // Usar cliente del navegador\n        const { user: clientUser, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!clientUser || authError) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').insert([\n            {\n                user_id: clientUser.id,\n                temario_id: temarioId,\n                titulo: titulo || 'Plan de Estudios',\n                plan_data: planData,\n                activo: true,\n                version: 1\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al guardar plan de estudios:', error);\n            return null;\n        }\n        console.log('✅ Plan de estudios guardado exitosamente:', data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error al guardar plan de estudios:', error);\n        return null;\n    }\n}\n/**\n * Guarda un plan de estudios generado en la base de datos (versión para servidor)\n */ async function guardarPlanEstudiosServidor(temarioId, planData, user, titulo) {\n    try {\n        const { createServerSupabaseClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\"));\n        const supabase = await createServerSupabaseClient();\n        const { data, error } = await supabase.from('planes_estudios').insert([\n            {\n                user_id: user.id,\n                temario_id: temarioId,\n                titulo: titulo || 'Plan de Estudios',\n                plan_data: planData,\n                activo: true,\n                version: 1\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al guardar plan de estudios (servidor):', error);\n            return null;\n        }\n        console.log('✅ Plan de estudios guardado exitosamente (servidor):', data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error al guardar plan de estudios (servidor):', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los planes de estudios de un temario (historial)\n */ async function obtenerHistorialPlanes(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('*').eq('user_id', user.id).eq('temario_id', temarioId).order('fecha_generacion', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener historial de planes:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener historial de planes:', error);\n        return [];\n    }\n}\n/**\n * Actualiza las notas de un plan de estudios\n */ async function actualizarNotasPlan(planId, notas) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            notas,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al actualizar notas del plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar notas del plan:', error);\n        return false;\n    }\n}\n/**\n * Marca un plan como activo y desactiva los demás\n */ async function activarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            activo: true,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al activar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al activar plan:', error);\n        return false;\n    }\n}\n/**\n * Elimina un plan de estudios\n */ async function eliminarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').delete().eq('id', planId);\n        if (error) {\n            console.error('Error al eliminar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar plan:', error);\n        return false;\n    }\n}\n/**\n * Guarda el progreso de una tarea del plan\n */ async function guardarProgresoTarea(planId, semanaNúmero, diaNombre, tareaTitulo, tareaTipo, completado, tiempoRealMinutos, notasProgreso, calificacion) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return false;\n        }\n        // Verificar si ya existe un registro de progreso para esta tarea\n        const { data: existente } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('id').eq('plan_id', planId).eq('user_id', user.id).eq('semana_numero', semanaNúmero).eq('dia_nombre', diaNombre).eq('tarea_titulo', tareaTitulo).single();\n        if (existente) {\n            // Actualizar registro existente\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').update({\n                completado,\n                fecha_completado: completado ? new Date().toISOString() : null,\n                tiempo_real_minutos: tiempoRealMinutos,\n                notas_progreso: notasProgreso,\n                calificacion,\n                actualizado_en: new Date().toISOString()\n            }).eq('id', existente.id);\n            if (error) {\n                console.error('Error al actualizar progreso:', error);\n                return false;\n            }\n        } else {\n            // Crear nuevo registro\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').insert([\n                {\n                    plan_id: planId,\n                    user_id: user.id,\n                    semana_numero: semanaNúmero,\n                    dia_nombre: diaNombre,\n                    tarea_titulo: tareaTitulo,\n                    tarea_tipo: tareaTipo,\n                    completado,\n                    fecha_completado: completado ? new Date().toISOString() : null,\n                    tiempo_real_minutos: tiempoRealMinutos,\n                    notas_progreso: notasProgreso,\n                    calificacion\n                }\n            ]);\n            if (error) {\n                console.error('Error al crear progreso:', error);\n                return false;\n            }\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al guardar progreso de tarea:', error);\n        return false;\n    }\n}\n/**\n * Obtiene el progreso de un plan de estudios\n */ async function obtenerProgresoPlan(planId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('*').eq('plan_id', planId).eq('user_id', user.id).order('semana_numero', {\n            ascending: true\n        }).order('creado_en', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener progreso del plan:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener progreso del plan:', error);\n        return [];\n    }\n}\n/**\n * Obtiene estadísticas del progreso del plan\n */ async function obtenerEstadisticasProgreso(planId) {\n    try {\n        const progreso = await obtenerProgresoPlan(planId);\n        const plan = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('plan_data').eq('id', planId).single();\n        if (!plan.data) {\n            return {\n                totalTareas: 0,\n                tareasCompletadas: 0,\n                porcentajeCompletado: 0,\n                tiempoTotalEstimado: 0,\n                tiempoTotalReal: 0,\n                semanasCompletadas: 0,\n                totalSemanas: 0\n            };\n        }\n        const planData = plan.data.plan_data;\n        const totalSemanas = planData.semanas.length;\n        // Calcular total de tareas\n        let totalTareas = 0;\n        planData.semanas.forEach((semana)=>{\n            semana.dias.forEach((dia)=>{\n                totalTareas += dia.tareas.length;\n            });\n        });\n        const tareasCompletadas = progreso.filter((p)=>p.completado).length;\n        const porcentajeCompletado = totalTareas > 0 ? tareasCompletadas / totalTareas * 100 : 0;\n        const tiempoTotalReal = progreso.filter((p)=>p.tiempo_real_minutos).reduce((total, p)=>total + (p.tiempo_real_minutos || 0), 0);\n        // Calcular semanas completadas (todas las tareas de la semana completadas)\n        let semanasCompletadas = 0;\n        planData.semanas.forEach((semana)=>{\n            const tareasSemanaTotales = semana.dias.reduce((total, dia)=>total + dia.tareas.length, 0);\n            const tareasSemanCompletadas = progreso.filter((p)=>p.semana_numero === semana.numero && p.completado).length;\n            if (tareasSemanaTotales > 0 && tareasSemanCompletadas === tareasSemanaTotales) {\n                semanasCompletadas++;\n            }\n        });\n        return {\n            totalTareas,\n            tareasCompletadas,\n            porcentajeCompletado: Math.round(porcentajeCompletado * 100) / 100,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal,\n            semanasCompletadas,\n            totalSemanas\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas de progreso:', error);\n        return {\n            totalTareas: 0,\n            tareasCompletadas: 0,\n            porcentajeCompletado: 0,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal: 0,\n            semanasCompletadas: 0,\n            totalSemanas: 0\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/planificacion/services/planEstudiosService.ts\n");

/***/ })

};
;