/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2ZlYXR1cmVzJTVDJTVDc2hhcmVkJTVDJTVDY29tcG9uZW50cyU1QyU1Q0NsaWVudExheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TUFBcUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFxPcG9zSSB2N1xcXFxzcmNcXFxcZmVhdHVyZXNcXFxcc2hhcmVkXFxcXGNvbXBvbmVudHNcXFxcQ2xpZW50TGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b63ad14717ba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjYzYWQxNDcxN2JhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/shared/components/ClientLayout */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\");\n\n\n\nconst metadata = {\n    title: 'OposiAI - Asistente IA para Oposiciones',\n    description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN1QjtBQUM4QztBQUU5RCxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNkLDRFQUFDVCxnRkFBWUE7MEJBQ1ZLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnO1xuaW1wb3J0IENsaWVudExheW91dCBmcm9tICdAL2ZlYXR1cmVzL3NoYXJlZC9jb21wb25lbnRzL0NsaWVudExheW91dCc7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnT3Bvc2lBSSAtIEFzaXN0ZW50ZSBJQSBwYXJhIE9wb3NpY2lvbmVzJyxcbiAgZGVzY3JpcHRpb246ICdBcGxpY2FjacOzbiBkZSBwcmVndW50YXMgeSByZXNwdWVzdGFzIGNvbiBJQSBwYXJhIHRlbWFyaW9zIGRlIG9wb3NpY2lvbmVzJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVzXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJmb250LXNhbnMgYmctZ3JheS0xMDBcIj5cbiAgICAgICAgPENsaWVudExheW91dD5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvQ2xpZW50TGF5b3V0PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJDbGllbnRMYXlvdXQiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\features\\shared\\components\\ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(ssr)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2ZlYXR1cmVzJTVDJTVDc2hhcmVkJTVDJTVDY29tcG9uZW50cyU1QyU1Q0NsaWVudExheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TUFBcUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFxPcG9zSSB2N1xcXFxzcmNcXFxcZmVhdHVyZXNcXFxcc2hhcmVkXFxcXGNvbXBvbmVudHNcXFxcQ2xpZW50TGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(ssr)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _features_auth_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/auth/hooks/useInactivityTimer */ \"(ssr)/./src/features/auth/hooks/useInactivityTimer.ts\");\n/* harmony import */ var _features_auth_components_InactivityWarning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/auth/components/InactivityWarning */ \"(ssr)/./src/features/auth/components/InactivityWarning.tsx\");\n\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start true: loading initial auth state\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInactivityWarning, setShowInactivityWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [warningTimeRemaining, setWarningTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60); // 60 segundos de advertencia\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Effect for auth state listener and initial session check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true); // Explicitly set loading true at the start of auth setup\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, currentSession)=>{\n                    setSession(currentSession);\n                    setUser(currentSession?.user ?? null);\n                    setError(null); // Clear previous errors on any auth state change\n                    // Centralize setIsLoading(false) after processing the event.\n                    if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.\n            _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session: initialSessionCheck }, error: getSessionError })=>{\n                    if (getSessionError) {\n                        setError(getSessionError.message);\n                        setIsLoading(false); // Ensure loading is false if initial getSession fails\n                    }\n                    // Para dispositivos móviles, verificar también localStorage si no hay sesión\n                    if (!initialSessionCheck && \"undefined\" !== 'undefined') {}\n                // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.\n                // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.\n                }\n            }[\"AuthProvider.useEffect\"]).catch({\n                \"AuthProvider.useEffect\": (error)=>{\n                    setError(error.message);\n                    setIsLoading(false); // Ensure loading is false if initial getSession throws\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Runs once on mount\n    // Effect for handling redirections based on auth state and pathname\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // No realizar redirecciones mientras se está cargando\n            if (isLoading) {\n                return;\n            }\n            // No aplicar redirecciones a rutas de API o recursos estáticos\n            if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {\n                return;\n            }\n            // Definir rutas públicas (mantener sincronizado con middleware)\n            const publicPaths = [\n                '/login'\n            ];\n            // Si hay sesión y estamos en /login, el middleware ya debería haber redirigido.\n            // Esta es una salvaguarda del lado del cliente.\n            if (session && pathname === '/login') {\n                router.replace('/'); // router.replace es mejor aquí para evitar entradas en el historial\n                return; // Importante retornar para no evaluar la siguiente condición\n            }\n            // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)\n            // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)\n            if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {\n                router.replace('/login');\n                return;\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        session,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const iniciarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[iniciarSesion]\": async (email, password_provided)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const { user: loggedInUser, session: currentAuthSession, error: loginError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.iniciarSesion)(email, password_provided);\n                if (loginError) {\n                    setError(loginError);\n                    setIsLoading(false); // Ensure loading is false on error\n                    return {\n                        user: null,\n                        session: null,\n                        error: loginError\n                    };\n                }\n                // Verificar que la sesión se haya establecido correctamente antes de redirigir\n                if (currentAuthSession) {\n                    // Esperar un momento adicional para asegurar que las cookies se propaguen\n                    // antes de la redirección\n                    await new Promise({\n                        \"AuthProvider.useCallback[iniciarSesion]\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"AuthProvider.useCallback[iniciarSesion]\"]);\n                    // Redirigir a la página principal usando replace para evitar entradas en el historial\n                    router.replace('/');\n                }\n                // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.\n                return {\n                    user: loggedInUser,\n                    session: currentAuthSession,\n                    error: null\n                };\n            } catch (e) {\n                const errorMessage = e instanceof Error && e.message ? e.message : 'Error desconocido durante el inicio de sesión.';\n                setError(errorMessage);\n                setIsLoading(false); // Ensure loading is false on exception\n                return {\n                    user: null,\n                    session: null,\n                    error: errorMessage\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[iniciarSesion]\"], [\n        router\n    ]); // Added router dependency\n    const cerrarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[cerrarSesion]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            const { error: logoutError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.cerrarSesion)();\n            if (logoutError) {\n                setError(logoutError);\n                setIsLoading(false); // Ensure loading is false on error\n            }\n        // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.\n        // The redirection useEffect will then handle redirecting to /login.\n        }\n    }[\"AuthProvider.useCallback[cerrarSesion]\"], []); // Assuming cerrarSesionService is a stable import\n    const estaAutenticado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[estaAutenticado]\": ()=>!!user && !!session && !isLoading\n    }[\"AuthProvider.useCallback[estaAutenticado]\"], [\n        user,\n        session,\n        isLoading\n    ]);\n    // Función para manejar el logout por inactividad\n    const handleInactivityLogout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleInactivityLogout]\": async ()=>{\n            // Cerrar sesión directamente sin mostrar advertencia\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleInactivityLogout]\"], [\n        cerrarSesion\n    ]);\n    // Función para extender la sesión\n    const handleExtendSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleExtendSession]\": ()=>{\n            setShowInactivityWarning(false);\n        // El hook useAutoLogout se reiniciará automáticamente con la actividad\n        }\n    }[\"AuthProvider.useCallback[handleExtendSession]\"], []);\n    // Función para cerrar sesión desde la advertencia\n    const handleLogoutFromWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLogoutFromWarning]\": async ()=>{\n            setShowInactivityWarning(false);\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleLogoutFromWarning]\"], [\n        cerrarSesion\n    ]);\n    // Hook para manejar la inactividad (solo si el usuario está autenticado)\n    const { resetTimer } = (0,_features_auth_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__.useAutoLogout)(10, handleInactivityLogout, estaAutenticado() // Solo activo si está autenticado\n    );\n    const value = {\n        user,\n        session,\n        isLoading,\n        error,\n        iniciarSesion,\n        cerrarSesion,\n        estaAutenticado\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: [\n            children,\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/BackgroundTasksContext.tsx":
/*!*************************************************!*\
  !*** ./src/contexts/BackgroundTasksContext.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundTasksProvider: () => (/* binding */ BackgroundTasksProvider),\n/* harmony export */   useBackgroundTasks: () => (/* binding */ useBackgroundTasks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ BackgroundTasksProvider,useBackgroundTasks auto */ \n\n\nconst BackgroundTasksContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst BackgroundTasksProvider = ({ children })=>{\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[addTask]\": (taskData)=>{\n            const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTask = {\n                ...taskData,\n                id,\n                status: 'pending',\n                createdAt: new Date()\n            };\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[addTask]\": (prev)=>[\n                        ...prev,\n                        newTask\n                    ]\n            }[\"BackgroundTasksProvider.useCallback[addTask]\"]);\n            // Mostrar notificación de inicio\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Iniciando: ${newTask.title}`, {\n                id: `task_start_${id}`,\n                duration: 2000\n            });\n            return id;\n        }\n    }[\"BackgroundTasksProvider.useCallback[addTask]\"], []);\n    const updateTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[updateTask]\": (id, updates)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[updateTask]\": (prev)=>prev.map({\n                        \"BackgroundTasksProvider.useCallback[updateTask]\": (task)=>{\n                            if (task.id === id) {\n                                const updatedTask = {\n                                    ...task,\n                                    ...updates\n                                };\n                                // Manejar notificaciones según el estado\n                                if (updates.status === 'processing' && task.status === 'pending') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Procesando: ${task.title}`, {\n                                        id: `task_processing_${id}`\n                                    });\n                                } else if (updates.status === 'completed' && task.status !== 'completed') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(`Completado: ${task.title}`, {\n                                        id: `task_completed_${id}`,\n                                        duration: 4000\n                                    });\n                                    updatedTask.completedAt = new Date();\n                                } else if (updates.status === 'error' && task.status !== 'error') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(`Error: ${task.title}`, {\n                                        id: `task_error_${id}`,\n                                        duration: 5000\n                                    });\n                                }\n                                return updatedTask;\n                            }\n                            return task;\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[updateTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[updateTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[updateTask]\"], []);\n    const removeTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[removeTask]\": (id)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[removeTask]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[removeTask]\": (task)=>task.id !== id\n                    }[\"BackgroundTasksProvider.useCallback[removeTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[removeTask]\"]);\n            // Limpiar notificaciones relacionadas\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_completed_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_error_${id}`);\n        }\n    }[\"BackgroundTasksProvider.useCallback[removeTask]\"], []);\n    const getTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTask]\": (id)=>{\n            return tasks.find({\n                \"BackgroundTasksProvider.useCallback[getTask]\": (task)=>task.id === id\n            }[\"BackgroundTasksProvider.useCallback[getTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTask]\"], [\n        tasks\n    ]);\n    const getTasksByType = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTasksByType]\": (type)=>{\n            return tasks.filter({\n                \"BackgroundTasksProvider.useCallback[getTasksByType]\": (task)=>task.type === type\n            }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"], [\n        tasks\n    ]);\n    const clearCompletedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": ()=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (task)=>task.status !== 'completed' && task.status !== 'error'\n                    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"])\n            }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"], []);\n    const activeTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[activeTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[activeTasks]\": (task)=>task.status === 'pending' || task.status === 'processing'\n            }[\"BackgroundTasksProvider.useMemo[activeTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[activeTasks]\"], [\n        tasks\n    ]);\n    const completedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[completedTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[completedTasks]\": (task)=>task.status === 'completed' || task.status === 'error'\n            }[\"BackgroundTasksProvider.useMemo[completedTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[completedTasks]\"], [\n        tasks\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[value]\": ()=>({\n                tasks,\n                addTask,\n                updateTask,\n                removeTask,\n                getTask,\n                getTasksByType,\n                clearCompletedTasks,\n                activeTasks,\n                completedTasks\n            })\n    }[\"BackgroundTasksProvider.useMemo[value]\"], [\n        tasks,\n        addTask,\n        updateTask,\n        removeTask,\n        getTask,\n        getTasksByType,\n        clearCompletedTasks,\n        activeTasks,\n        completedTasks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackgroundTasksContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\contexts\\\\BackgroundTasksContext.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\nconst useBackgroundTasks = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BackgroundTasksContext);\n    if (context === undefined) {\n        throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/BackgroundTasksContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/AuthManager.tsx":
/*!******************************************************!*\
  !*** ./src/features/auth/components/AuthManager.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Componente para manejar errores comunes de autenticación\n * y sincronización de tiempo en Supabase\n */ function AuthManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthManager.useEffect\": ()=>{\n            // Verificar si hay problemas de sincronización de tiempo\n            const checkTimeSync = {\n                \"AuthManager.useEffect.checkTimeSync\": async ()=>{\n                    try {\n                        // Usar variables de entorno para la configuración de Supabase\n                        const supabaseUrl = \"https://fxnhpxjijinfuxxxplzj.supabase.co\";\n                        const supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\";\n                        if (!supabaseUrl || !supabaseKey) {\n                            console.warn('Variables de entorno de Supabase no configuradas');\n                            return;\n                        }\n                        // Obtener la hora del servidor de Supabase\n                        const response = await fetch(`${supabaseUrl}/rest/v1/`, {\n                            method: 'GET',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'apikey': supabaseKey\n                            }\n                        });\n                        // Obtener la fecha del servidor desde las cabeceras\n                        const serverDate = new Date(response.headers.get('date') || '');\n                        const clientDate = new Date();\n                        // Calcular la diferencia en segundos\n                        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);\n                        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia\n                        if (timeDiff > 60) {\n                            console.warn(`Posible problema de sincronización de tiempo detectado. ` + `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` + `Esto puede causar problemas de autenticación.`);\n                        }\n                    } catch (error) {\n                        console.error('Error al verificar sincronización de tiempo:', error);\n                    }\n                }\n            }[\"AuthManager.useEffect.checkTimeSync\"];\n            // Ejecutar la verificación\n            checkTimeSync();\n            // Configurar un listener para eventos de autenticación\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"AuthManager.useEffect\": (event, session)=>{\n                    if (event === 'SIGNED_OUT') {\n                    // Supabase ya maneja la limpieza de tokens internamente\n                    } else if (event === 'SIGNED_IN') {}\n                }\n            }[\"AuthManager.useEffect\"]);\n            return ({\n                \"AuthManager.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthManager.useEffect\"];\n        }\n    }[\"AuthManager.useEffect\"], []);\n    // Este componente no renderiza nada visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/AuthManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/InactivityWarning.tsx":
/*!************************************************************!*\
  !*** ./src/features/auth/components/InactivityWarning.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiClock,FiRefreshCw!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst InactivityWarning = ({ isVisible, timeRemaining, onExtendSession, onLogout })=>{\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(timeRemaining);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            setCountdown(timeRemaining);\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        timeRemaining\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            if (!isVisible || countdown <= 0) return;\n            const interval = setInterval({\n                \"InactivityWarning.useEffect.interval\": ()=>{\n                    setCountdown({\n                        \"InactivityWarning.useEffect.interval\": (prev)=>{\n                            if (prev <= 1) {\n                                onLogout();\n                                return 0;\n                            }\n                            return prev - 1;\n                        }\n                    }[\"InactivityWarning.useEffect.interval\"]);\n                }\n            }[\"InactivityWarning.useEffect.interval\"], 1000);\n            return ({\n                \"InactivityWarning.useEffect\": ()=>clearInterval(interval)\n            })[\"InactivityWarning.useEffect\"];\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        isVisible,\n        countdown,\n        onLogout\n    ]);\n    if (!isVisible) return null;\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-100 rounded-full p-3 mr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiAlertTriangle, {\n                                className: \"text-yellow-600 text-xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Sesi\\xf3n por expirar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Tu sesi\\xf3n expirar\\xe1 por inactividad\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                                    className: \"text-gray-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-mono font-bold text-gray-900\",\n                                    children: formatTime(countdown)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 mt-2\",\n                            children: \"Tiempo restante antes del cierre autom\\xe1tico\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onExtendSession,\n                            className: \"flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Continuar sesi\\xf3n\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onLogout,\n                            className: \"flex-1 bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n                            children: \"Cerrar sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Por seguridad, tu sesi\\xf3n se cerrar\\xe1 autom\\xe1ticamente tras 10 minutos de inactividad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InactivityWarning);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/InactivityWarning.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/hooks/useInactivityTimer.ts":
/*!*******************************************************!*\
  !*** ./src/features/auth/hooks/useInactivityTimer.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout),\n/* harmony export */   useInactivityTimer: () => (/* binding */ useInactivityTimer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n\n\n/**\n * Hook para manejar la desconexión automática por inactividad\n */ const useInactivityTimer = ({ timeout, onTimeout, enabled = true })=>{\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const { tasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__.useBackgroundTasks)();\n    // Verificar si hay tareas de IA activas que deberían pausar el timer\n    const hasActiveAITasks = tasks.some((task)=>task.status === 'pending' || task.status === 'processing');\n    // Función para resetear el timer\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n            if (!enabled) return;\n            // Limpiar el timer anterior si existe\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            // Actualizar la última actividad\n            lastActivityRef.current = Date.now();\n            // Siempre crear un nuevo timer, independientemente de las tareas de IA\n            // La actividad del usuario siempre reinicia el timer\n            timeoutRef.current = setTimeout({\n                \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n                    // Solo ejecutar logout si no hay tareas de IA activas en el momento del timeout\n                    const currentTasks = tasks.filter({\n                        \"useInactivityTimer.useCallback[resetTimer].currentTasks\": (task)=>task.status === 'pending' || task.status === 'processing'\n                    }[\"useInactivityTimer.useCallback[resetTimer].currentTasks\"]);\n                    if (currentTasks.length === 0) {\n                        onTimeout();\n                    } else {\n                        console.log('🔄 Logout diferido: hay tareas de IA en progreso, reintentando en 1 minuto');\n                        // Reintentar en 1 minuto si hay tareas activas\n                        setTimeout({\n                            \"useInactivityTimer.useCallback[resetTimer]\": ()=>resetTimer()\n                        }[\"useInactivityTimer.useCallback[resetTimer]\"], 60000);\n                    }\n                }\n            }[\"useInactivityTimer.useCallback[resetTimer]\"], timeout);\n        }\n    }[\"useInactivityTimer.useCallback[resetTimer]\"], [\n        timeout,\n        onTimeout,\n        enabled,\n        tasks\n    ]);\n    // Función para limpiar el timer\n    const clearTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[clearTimer]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n        }\n    }[\"useInactivityTimer.useCallback[clearTimer]\"], []);\n    // Función para obtener el tiempo restante\n    const getTimeRemaining = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[getTimeRemaining]\": ()=>{\n            if (!enabled || !timeoutRef.current) return 0;\n            const elapsed = Date.now() - lastActivityRef.current;\n            const remaining = Math.max(0, timeout - elapsed);\n            return remaining;\n        }\n    }[\"useInactivityTimer.useCallback[getTimeRemaining]\"], [\n        timeout,\n        enabled\n    ]);\n    // Eventos que consideramos como actividad del usuario\n    const events = [\n        'mousedown',\n        'mousemove',\n        'keypress',\n        'scroll',\n        'touchstart',\n        'click',\n        'keydown'\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useInactivityTimer.useEffect\": ()=>{\n            if (!enabled) {\n                clearTimer();\n                return;\n            }\n            // Función que maneja los eventos de actividad\n            const handleActivity = {\n                \"useInactivityTimer.useEffect.handleActivity\": ()=>{\n                    resetTimer();\n                }\n            }[\"useInactivityTimer.useEffect.handleActivity\"];\n            // Agregar event listeners\n            events.forEach({\n                \"useInactivityTimer.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useInactivityTimer.useEffect\"]);\n            // Iniciar el timer\n            resetTimer();\n            // Cleanup\n            return ({\n                \"useInactivityTimer.useEffect\": ()=>{\n                    events.forEach({\n                        \"useInactivityTimer.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useInactivityTimer.useEffect\"]);\n                    clearTimer();\n                }\n            })[\"useInactivityTimer.useEffect\"];\n        }\n    }[\"useInactivityTimer.useEffect\"], [\n        enabled,\n        resetTimer,\n        clearTimer\n    ]);\n    return {\n        resetTimer,\n        clearTimer,\n        getTimeRemaining\n    };\n};\n/**\n * Hook simplificado para desconexión automática\n */ const useAutoLogout = (timeoutMinutes = 5, onLogout, enabled = true)=>{\n    const timeoutMs = timeoutMinutes * 60 * 1000; // Convertir minutos a milisegundos\n    return useInactivityTimer({\n        timeout: timeoutMs,\n        onTimeout: onLogout,\n        enabled\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/hooks/useInactivityTimer.ts\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/shared/components/BackgroundTasksPanel.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BackgroundTasksPanel = ()=>{\n    const { activeTasks, completedTasks, removeTask, clearCompletedTasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__.useBackgroundTasks)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCompleted, setShowCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const totalTasks = activeTasks.length + completedTasks.length;\n    if (totalTasks === 0) {\n        return null;\n    }\n    const getTaskIcon = (task)=>{\n        switch(task.status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 16\n                }, undefined);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 16\n                }, undefined);\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 16\n                }, undefined);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTaskTypeLabel = (type)=>{\n        switch(type){\n            case 'mapa-mental':\n                return 'Mapa Mental';\n            case 'test':\n                return 'Test';\n            case 'flashcards':\n                return 'Flashcards';\n            default:\n                return 'Tarea';\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('es-ES', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between\",\n                    onClick: ()=>setIsExpanded(!isExpanded),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"Tareas (\",\n                                        activeTasks.length,\n                                        \" activas)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-96 overflow-y-auto\",\n                    children: [\n                        activeTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border-b border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-gray-700 mb-2\",\n                                    children: \"Tareas Activas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: activeTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        task.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-200 rounded-full h-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-500 h-1.5 rounded-full transition-all duration-300\",\n                                                                    style: {\n                                                                        width: `${task.progress}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: formatTime(task.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, undefined),\n                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompleted(!showCompleted),\n                                            className: \"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Completadas (\",\n                                                        completedTasks.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                showCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearCompletedTasks,\n                                            className: \"text-xs text-gray-500 hover:text-red-600 transition-colors\",\n                                            children: \"Limpiar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, undefined),\n                                showCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: completedTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        task.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-500 truncate\",\n                                                            children: task.error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: task.completedAt ? formatTime(task.completedAt) : formatTime(task.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeTask(task.id),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackgroundTasksPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvZmVhdHVyZXMvc2hhcmVkL2NvbXBvbmVudHMvQmFja2dyb3VuZFRhc2tzUGFuZWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXdDO0FBQytDO0FBU2xEO0FBRXJDLE1BQU1VLHVCQUFpQztJQUNyQyxNQUFNLEVBQUVDLFdBQVcsRUFBRUMsY0FBYyxFQUFFQyxVQUFVLEVBQUVDLG1CQUFtQixFQUFFLEdBQUdaLG9GQUFrQkE7SUFDM0YsTUFBTSxDQUFDYSxZQUFZQyxjQUFjLEdBQUdmLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2dCLGVBQWVDLGlCQUFpQixHQUFHakIsK0NBQVFBLENBQUM7SUFFbkQsTUFBTWtCLGFBQWFSLFlBQVlTLE1BQU0sR0FBR1IsZUFBZVEsTUFBTTtJQUU3RCxJQUFJRCxlQUFlLEdBQUc7UUFDcEIsT0FBTztJQUNUO0lBRUEsTUFBTUUsY0FBYyxDQUFDQztRQUNuQixPQUFRQSxLQUFLQyxNQUFNO1lBQ2pCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNmLCtMQUFTQTtvQkFBQ2dCLFdBQVU7Ozs7OztZQUM5QixLQUFLO2dCQUNILHFCQUFPLDhEQUFDZiwrTEFBYUE7b0JBQUNlLFdBQVU7Ozs7OztZQUNsQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDbEIsK0xBQWVBO29CQUFDa0IsV0FBVTs7Ozs7O1lBQ3BDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNqQiwrTEFBcUJBO29CQUFDaUIsV0FBVTs7Ozs7O1lBQzFDO2dCQUNFLHFCQUFPLDhEQUFDaEIsK0xBQVNBO29CQUFDZ0IsV0FBVTs7Ozs7O1FBQ2hDO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUIsQ0FBQ0M7UUFDeEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1DLGFBQWEsQ0FBQ0M7UUFDbEIsT0FBT0EsS0FBS0Msa0JBQWtCLENBQUMsU0FBUztZQUN0Q0MsTUFBTTtZQUNOQyxRQUFRO1FBQ1Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDQztRQUFJUixXQUFVO2tCQUViLDRFQUFDUTtZQUFJUixXQUFVOzs4QkFFYiw4REFBQ1E7b0JBQ0NSLFdBQVU7b0JBQ1ZTLFNBQVMsSUFBTWpCLGNBQWMsQ0FBQ0Q7O3NDQUU5Qiw4REFBQ2lCOzRCQUFJUixXQUFVOzs4Q0FDYiw4REFBQ2YsK0xBQWFBO29DQUFDZSxXQUFVOzs7Ozs7OENBQ3pCLDhEQUFDVTtvQ0FBS1YsV0FBVTs7d0NBQWM7d0NBQ25CYixZQUFZUyxNQUFNO3dDQUFDOzs7Ozs7Ozs7Ozs7O3dCQUcvQkwsMkJBQ0MsOERBQUNaLCtMQUFlQTs0QkFBQ3FCLFdBQVU7Ozs7O3NEQUUzQiw4REFBQ3BCLCtMQUFhQTs0QkFBQ29CLFdBQVU7Ozs7Ozs7Ozs7OztnQkFLNUJULDRCQUNDLDhEQUFDaUI7b0JBQUlSLFdBQVU7O3dCQUVaYixZQUFZUyxNQUFNLEdBQUcsbUJBQ3BCLDhEQUFDWTs0QkFBSVIsV0FBVTs7OENBQ2IsOERBQUNXO29DQUFHWCxXQUFVOzhDQUEyQzs7Ozs7OzhDQUd6RCw4REFBQ1E7b0NBQUlSLFdBQVU7OENBQ1piLFlBQVl5QixHQUFHLENBQUMsQ0FBQ2QscUJBQ2hCLDhEQUFDVTs0Q0FFQ1IsV0FBVTs7Z0RBRVRILFlBQVlDOzhEQUNiLDhEQUFDVTtvREFBSVIsV0FBVTs7c0VBQ2IsOERBQUNhOzREQUFFYixXQUFVO3NFQUNWQyxpQkFBaUJILEtBQUtJLElBQUk7Ozs7OztzRUFFN0IsOERBQUNXOzREQUFFYixXQUFVO3NFQUNWRixLQUFLZ0IsS0FBSzs7Ozs7O3dEQUVaaEIsS0FBS2lCLFFBQVEsS0FBS0MsMkJBQ2pCLDhEQUFDUjs0REFBSVIsV0FBVTtzRUFDYiw0RUFBQ1E7Z0VBQUlSLFdBQVU7MEVBQ2IsNEVBQUNRO29FQUNDUixXQUFVO29FQUNWaUIsT0FBTzt3RUFBRUMsT0FBTyxHQUFHcEIsS0FBS2lCLFFBQVEsQ0FBQyxDQUFDLENBQUM7b0VBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTTlDLDhEQUFDTDtvREFBS1YsV0FBVTs4REFDYkcsV0FBV0wsS0FBS3FCLFNBQVM7Ozs7Ozs7MkNBdkJ2QnJCLEtBQUtzQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3dCQWdDckJoQyxlQUFlUSxNQUFNLEdBQUcsbUJBQ3ZCLDhEQUFDWTs0QkFBSVIsV0FBVTs7OENBQ2IsOERBQUNRO29DQUFJUixXQUFVOztzREFDYiw4REFBQ3FCOzRDQUNDWixTQUFTLElBQU1mLGlCQUFpQixDQUFDRDs0Q0FDakNPLFdBQVU7OzhEQUVWLDhEQUFDVTs7d0RBQUs7d0RBQWN0QixlQUFlUSxNQUFNO3dEQUFDOzs7Ozs7O2dEQUN6Q0gsOEJBQ0MsOERBQUNiLCtMQUFhQTtvREFBQ29CLFdBQVU7Ozs7OzhFQUV6Qiw4REFBQ3JCLCtMQUFlQTtvREFBQ3FCLFdBQVU7Ozs7Ozs7Ozs7Ozt3Q0FHOUJaLGVBQWVRLE1BQU0sR0FBRyxtQkFDdkIsOERBQUN5Qjs0Q0FDQ1osU0FBU25COzRDQUNUVSxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Z0NBTUpQLCtCQUNDLDhEQUFDZTtvQ0FBSVIsV0FBVTs4Q0FDWlosZUFBZXdCLEdBQUcsQ0FBQyxDQUFDZCxxQkFDbkIsOERBQUNVOzRDQUVDUixXQUFVOztnREFFVEgsWUFBWUM7OERBQ2IsOERBQUNVO29EQUFJUixXQUFVOztzRUFDYiw4REFBQ2E7NERBQUViLFdBQVU7c0VBQ1ZDLGlCQUFpQkgsS0FBS0ksSUFBSTs7Ozs7O3NFQUU3Qiw4REFBQ1c7NERBQUViLFdBQVU7c0VBQ1ZGLEtBQUtnQixLQUFLOzs7Ozs7d0RBRVpoQixLQUFLd0IsS0FBSyxrQkFDVCw4REFBQ1Q7NERBQUViLFdBQVU7c0VBQ1ZGLEtBQUt3QixLQUFLOzs7Ozs7Ozs7Ozs7OERBSWpCLDhEQUFDZDtvREFBSVIsV0FBVTs7c0VBQ2IsOERBQUNVOzREQUFLVixXQUFVO3NFQUNiRixLQUFLeUIsV0FBVyxHQUFHcEIsV0FBV0wsS0FBS3lCLFdBQVcsSUFBSXBCLFdBQVdMLEtBQUtxQixTQUFTOzs7Ozs7c0VBRTlFLDhEQUFDRTs0REFDQ1osU0FBUyxJQUFNcEIsV0FBV1MsS0FBS3NCLEVBQUU7NERBQ2pDcEIsV0FBVTtzRUFFViw0RUFBQ25CLCtMQUFTQTtnRUFBQ21CLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0F6QnBCRixLQUFLc0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBdUNwQztBQUVBLGlFQUFlbEMsb0JBQW9CQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxmZWF0dXJlc1xcc2hhcmVkXFxjb21wb25lbnRzXFxCYWNrZ3JvdW5kVGFza3NQYW5lbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VCYWNrZ3JvdW5kVGFza3MsIEJhY2tncm91bmRUYXNrIH0gZnJvbSAnQC9jb250ZXh0cy9CYWNrZ3JvdW5kVGFza3NDb250ZXh0JztcbmltcG9ydCB7IFxuICBDaGV2cm9uRG93bkljb24sIFxuICBDaGV2cm9uVXBJY29uLCBcbiAgWE1hcmtJY29uLFxuICBDaGVja0NpcmNsZUljb24sXG4gIEV4Y2xhbWF0aW9uQ2lyY2xlSWNvbixcbiAgQ2xvY2tJY29uLFxuICBDb2c2VG9vdGhJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5cbmNvbnN0IEJhY2tncm91bmRUYXNrc1BhbmVsOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgeyBhY3RpdmVUYXNrcywgY29tcGxldGVkVGFza3MsIHJlbW92ZVRhc2ssIGNsZWFyQ29tcGxldGVkVGFza3MgfSA9IHVzZUJhY2tncm91bmRUYXNrcygpO1xuICBjb25zdCBbaXNFeHBhbmRlZCwgc2V0SXNFeHBhbmRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93Q29tcGxldGVkLCBzZXRTaG93Q29tcGxldGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCB0b3RhbFRhc2tzID0gYWN0aXZlVGFza3MubGVuZ3RoICsgY29tcGxldGVkVGFza3MubGVuZ3RoO1xuXG4gIGlmICh0b3RhbFRhc2tzID09PSAwKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICBjb25zdCBnZXRUYXNrSWNvbiA9ICh0YXNrOiBCYWNrZ3JvdW5kVGFzaykgPT4ge1xuICAgIHN3aXRjaCAodGFzay5zdGF0dXMpIHtcbiAgICAgIGNhc2UgJ3BlbmRpbmcnOlxuICAgICAgICByZXR1cm4gPENsb2NrSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQteWVsbG93LTUwMFwiIC8+O1xuICAgICAgY2FzZSAncHJvY2Vzc2luZyc6XG4gICAgICAgIHJldHVybiA8Q29nNlRvb3RoSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYmx1ZS01MDAgYW5pbWF0ZS1zcGluXCIgLz47XG4gICAgICBjYXNlICdjb21wbGV0ZWQnOlxuICAgICAgICByZXR1cm4gPENoZWNrQ2lyY2xlSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNTAwXCIgLz47XG4gICAgICBjYXNlICdlcnJvcic6XG4gICAgICAgIHJldHVybiA8RXhjbGFtYXRpb25DaXJjbGVJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNTAwXCIgLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPENsb2NrSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS01MDBcIiAvPjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0VGFza1R5cGVMYWJlbCA9ICh0eXBlOiBCYWNrZ3JvdW5kVGFza1sndHlwZSddKSA9PiB7XG4gICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICBjYXNlICdtYXBhLW1lbnRhbCc6XG4gICAgICAgIHJldHVybiAnTWFwYSBNZW50YWwnO1xuICAgICAgY2FzZSAndGVzdCc6XG4gICAgICAgIHJldHVybiAnVGVzdCc7XG4gICAgICBjYXNlICdmbGFzaGNhcmRzJzpcbiAgICAgICAgcmV0dXJuICdGbGFzaGNhcmRzJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnVGFyZWEnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmb3JtYXRUaW1lID0gKGRhdGU6IERhdGUpID0+IHtcbiAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoJ2VzLUVTJywgeyBcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JywgXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0JyBcbiAgICB9KTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgYm90dG9tLTQgcmlnaHQtNCB6LTUwIG1heC13LXNtXCI+XG4gICAgICB7LyogUGFuZWwgcHJpbmNpcGFsICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IFxuICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1wdXJwbGUtNjAwIHRleHQtd2hpdGUgcC0zIGN1cnNvci1wb2ludGVyIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNFeHBhbmRlZCghaXNFeHBhbmRlZCl9XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPENvZzZUb290aEljb24gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICBUYXJlYXMgKHthY3RpdmVUYXNrcy5sZW5ndGh9IGFjdGl2YXMpXG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAge2lzRXhwYW5kZWQgPyAoXG4gICAgICAgICAgICA8Q2hldnJvbkRvd25JY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8Q2hldnJvblVwSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ29udGVuaWRvIGV4cGFuZGlibGUgKi99XG4gICAgICAgIHtpc0V4cGFuZGVkICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLTk2IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgey8qIFRhcmVhcyBhY3RpdmFzICovfVxuICAgICAgICAgICAge2FjdGl2ZVRhc2tzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBib3JkZXItYiBib3JkZXItZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgVGFyZWFzIEFjdGl2YXNcbiAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICB7YWN0aXZlVGFza3MubWFwKCh0YXNrKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgICAgICAga2V5PXt0YXNrLmlkfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBwLTIgYmctZ3JheS01MCByb3VuZGVkLW1kXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRUYXNrSWNvbih0YXNrKX1cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2dldFRhc2tUeXBlTGFiZWwodGFzay50eXBlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzay50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt0YXNrLnByb2dyZXNzICE9PSB1bmRlZmluZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTEuNVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaC0xLjUgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHt0YXNrLnByb2dyZXNzfSVgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0VGltZSh0YXNrLmNyZWF0ZWRBdCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBUYXJlYXMgY29tcGxldGFkYXMgKi99XG4gICAgICAgICAgICB7Y29tcGxldGVkVGFza3MubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q29tcGxldGVkKCFzaG93Q29tcGxldGVkKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Db21wbGV0YWRhcyAoe2NvbXBsZXRlZFRhc2tzLmxlbmd0aH0pPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB7c2hvd0NvbXBsZXRlZCA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvblVwSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd25JY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICB7Y29tcGxldGVkVGFza3MubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbGVhckNvbXBsZXRlZFRhc2tzfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LXJlZC02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgTGltcGlhclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7c2hvd0NvbXBsZXRlZCAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICB7Y29tcGxldGVkVGFza3MubWFwKCh0YXNrKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17dGFzay5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBwLTIgYmctZ3JheS01MCByb3VuZGVkLW1kXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Z2V0VGFza0ljb24odGFzayl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRUYXNrVHlwZUxhYmVsKHRhc2sudHlwZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2sudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Rhc2suZXJyb3IgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtNTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFzay5lcnJvcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0YXNrLmNvbXBsZXRlZEF0ID8gZm9ybWF0VGltZSh0YXNrLmNvbXBsZXRlZEF0KSA6IGZvcm1hdFRpbWUodGFzay5jcmVhdGVkQXQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVUYXNrKHRhc2suaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1yZWQtNTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxYTWFya0ljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBCYWNrZ3JvdW5kVGFza3NQYW5lbDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlQmFja2dyb3VuZFRhc2tzIiwiQ2hldnJvbkRvd25JY29uIiwiQ2hldnJvblVwSWNvbiIsIlhNYXJrSWNvbiIsIkNoZWNrQ2lyY2xlSWNvbiIsIkV4Y2xhbWF0aW9uQ2lyY2xlSWNvbiIsIkNsb2NrSWNvbiIsIkNvZzZUb290aEljb24iLCJCYWNrZ3JvdW5kVGFza3NQYW5lbCIsImFjdGl2ZVRhc2tzIiwiY29tcGxldGVkVGFza3MiLCJyZW1vdmVUYXNrIiwiY2xlYXJDb21wbGV0ZWRUYXNrcyIsImlzRXhwYW5kZWQiLCJzZXRJc0V4cGFuZGVkIiwic2hvd0NvbXBsZXRlZCIsInNldFNob3dDb21wbGV0ZWQiLCJ0b3RhbFRhc2tzIiwibGVuZ3RoIiwiZ2V0VGFza0ljb24iLCJ0YXNrIiwic3RhdHVzIiwiY2xhc3NOYW1lIiwiZ2V0VGFza1R5cGVMYWJlbCIsInR5cGUiLCJmb3JtYXRUaW1lIiwiZGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJkaXYiLCJvbkNsaWNrIiwic3BhbiIsImg0IiwibWFwIiwicCIsInRpdGxlIiwicHJvZ3Jlc3MiLCJ1bmRlZmluZWQiLCJzdHlsZSIsIndpZHRoIiwiY3JlYXRlZEF0IiwiaWQiLCJidXR0b24iLCJlcnJvciIsImNvbXBsZXRlZEF0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../auth/components/AuthManager */ \"(ssr)/./src/features/auth/components/AuthManager.tsx\");\n/* harmony import */ var _BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BackgroundTasksPanel */ \"(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n // Importar Toaster\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__.BackgroundTasksProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.Toaster, {\n                    position: \"top-right\" // Posición de los toasts\n                    ,\n                    toastOptions: {\n                        // Opciones por defecto para los toasts\n                        duration: 5000,\n                        style: {\n                            background: '#363636',\n                            color: '#fff'\n                        },\n                        success: {\n                            duration: 3000,\n                            style: {\n                                background: '#10b981',\n                                color: '#fff'\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            style: {\n                                background: '#ef4444',\n                                color: '#fff'\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\n * Inicia sesión con email y contraseña\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\n * Cierra la sesión del usuario actual\n */ async function cerrarSesion() {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\n * Obtiene la sesión actual del usuario\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\n * Obtiene el usuario actual\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\n * Verifica si el usuario está autenticado\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\");\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFFcEQsc0RBQXNEO0FBQy9DLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUM7QUFFN0M7QUFFQSwrQ0FBK0M7QUFDeEMsTUFBTUksV0FBV0wsZUFBZSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcbGliXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIG5hdmVnYWRvciAoY29tcG9uZW50ZXMgZGVsIGNsaWVudGUpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuICApO1xufVxuXG4vLyBNYW50ZW5lciBjb21wYXRpYmlsaWRhZCBjb24gY8OzZGlnbyBleGlzdGVudGVcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   supabase: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/supabase/client.ts\");\n// Solo re-exportar el cliente del navegador para mantener compatibilidad\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-icons","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/react-hot-toast","vendor-chunks/cookie","vendor-chunks/@heroicons","vendor-chunks/webidl-conversions","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();