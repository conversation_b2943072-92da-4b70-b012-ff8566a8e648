"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx":
/*!************************************************************!*\
  !*** ./src/features/temario/components/TemarioManager.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiEdit,FiSettings,FiTrash2,FiTrendingUp,FiZap!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _features_planificacion_components_PlanificacionAsistente__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/planificacion/components/PlanificacionAsistente */ \"(app-pages-browser)/./src/features/planificacion/components/PlanificacionAsistente.tsx\");\n/* harmony import */ var _TemarioEditModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TemarioEditModal */ \"(app-pages-browser)/./src/features/temario/components/TemarioEditModal.tsx\");\n/* harmony import */ var _TemaEditModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemaEditModal */ \"(app-pages-browser)/./src/features/temario/components/TemaEditModal.tsx\");\n/* harmony import */ var _TemaActions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TemaActions */ \"(app-pages-browser)/./src/features/temario/components/TemaActions.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TemarioManager = ()=>{\n    _s();\n    const [temario, setTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temas, setTemas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [actualizandoTema, setActualizandoTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarAsistentePlanificacion, setMostrarAsistentePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarModalEdicion, setMostrarModalEdicion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarModalEdicionTema, setMostrarModalEdicionTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [temaSeleccionado, setTemaSeleccionado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mostrarConfirmacionEliminar, setMostrarConfirmacionEliminar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eliminandoTemario, setEliminandoTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TemarioManager.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"TemarioManager.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            const temarioData = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemarioUsuario)();\n            if (temarioData) {\n                setTemario(temarioData);\n                const [temasData, estadisticasData, planificacionConfigurada] = await Promise.all([\n                    (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemas)(temarioData.id),\n                    (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temarioData.id),\n                    (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.tienePlanificacionConfigurada)(temarioData.id)\n                ]);\n                setTemas(temasData);\n                setEstadisticas(estadisticasData);\n                setTienePlanificacion(planificacionConfigurada);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del temario:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al cargar el temario');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleToggleCompletado = async (temaId, completado)=>{\n        setActualizandoTema(temaId);\n        try {\n            const exito = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.actualizarEstadoTema)(temaId, !completado);\n            if (exito) {\n                // Actualizar el estado local\n                setTemas(temas.map((tema)=>tema.id === temaId ? {\n                        ...tema,\n                        completado: !completado,\n                        fecha_completado: !completado ? new Date().toISOString() : undefined\n                    } : tema));\n                // Recalcular estadísticas\n                if (temario) {\n                    const nuevasEstadisticas = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temario.id);\n                    setEstadisticas(nuevasEstadisticas);\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success(!completado ? 'Tema marcado como completado' : 'Tema marcado como pendiente');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al actualizar el estado del tema');\n            }\n        } catch (error) {\n            console.error('Error al actualizar tema:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al actualizar el tema');\n        } finally{\n            setActualizandoTema(null);\n        }\n    };\n    const formatearFecha = (fecha)=>{\n        return new Date(fecha).toLocaleDateString('es-ES', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric'\n        });\n    };\n    const handleIniciarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(true);\n    };\n    const handleModificarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(true);\n    };\n    const handleCompletarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(false);\n        setTienePlanificacion(true);\n        // Recargar datos para reflejar los cambios\n        cargarDatos();\n    };\n    const handleCancelarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(false);\n    };\n    const handleEditarTemario = ()=>{\n        setMostrarModalEdicion(true);\n    };\n    const handleGuardarTemario = (temarioActualizado)=>{\n        setTemario(temarioActualizado);\n        setMostrarModalEdicion(false);\n    };\n    const handleCancelarEdicion = ()=>{\n        setMostrarModalEdicion(false);\n    };\n    const handleEditarTema = (tema)=>{\n        setTemaSeleccionado(tema);\n        setMostrarModalEdicionTema(true);\n    };\n    const handleGuardarTema = (temaActualizado)=>{\n        setTemas(temas.map((tema)=>tema.id === temaActualizado.id ? temaActualizado : tema));\n        setMostrarModalEdicionTema(false);\n        setTemaSeleccionado(null);\n    };\n    const handleCancelarEdicionTema = ()=>{\n        setMostrarModalEdicionTema(false);\n        setTemaSeleccionado(null);\n    };\n    const handleEliminarTema = async (temaId)=>{\n        // Eliminar tema del estado local\n        setTemas(temas.filter((tema)=>tema.id !== temaId));\n        // Recalcular estadísticas\n        if (temario) {\n            const nuevasEstadisticas = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temario.id);\n            setEstadisticas(nuevasEstadisticas);\n        }\n    };\n    const handleEliminarTemario = ()=>{\n        setMostrarConfirmacionEliminar(true);\n    };\n    const handleConfirmarEliminarTemario = async ()=>{\n        if (!temario) return;\n        setEliminandoTemario(true);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.loading('Eliminando temario y plan de estudios...');\n            // 1. Obtener y eliminar planificación si existe\n            const planificacion = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.obtenerPlanificacionUsuario)(temario.id);\n            if (planificacion) {\n                await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.eliminarPlanificacion)(planificacion.id);\n            }\n            // 2. Obtener y eliminar planes de estudios si existen\n            const planes = await obtenerHistorialPlanes(temario.id);\n            for (const plan of planes){\n                await eliminarPlan(plan.id);\n            }\n            // 3. Eliminar el temario (esto eliminará automáticamente los temas por CASCADE)\n            const success = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.eliminarTemario)(temario.id);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('Temario y plan de estudios eliminados exitosamente', {\n                    id: loadingToastId\n                });\n                // Limpiar estado y recargar\n                setTemario(null);\n                setTemas([]);\n                setEstadisticas(null);\n                setTienePlanificacion(false);\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al eliminar el temario', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar temario:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al eliminar el temario', {\n                id: loadingToastId\n            });\n        } finally{\n            setEliminandoTemario(false);\n            setMostrarConfirmacionEliminar(false);\n        }\n    };\n    const handleCancelarEliminarTemario = ()=>{\n        setMostrarConfirmacionEliminar(false);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!temario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiBook, {\n                    className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No hay temario configurado\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"Configura tu temario desde el dashboard para comenzar.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar asistente de planificación si está activo\n    if (mostrarAsistentePlanificacion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanificacionAsistente__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            temario: temario,\n            onComplete: handleCompletarPlanificacion,\n            onCancel: handleCancelarPlanificacion,\n            isEditing: tienePlanificacion\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: temario.titulo\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    temario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: temario.descripcion\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(temario.tipo === 'completo' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'),\n                                                children: temario.tipo === 'completo' ? 'Temario Completo' : 'Temas Sueltos'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"Creado el \",\n                                                    formatearFecha(temario.creado_en)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleEditarTemario,\n                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors\",\n                                        title: \"Editar temario\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiEdit, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleEliminarTemario,\n                                        className: \"p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors\",\n                                        title: \"Eliminar temario\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiTrash2, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiBook, {\n                                            className: \"w-5 h-5 text-blue-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Total Temas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: estadisticas.totalTemas\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                            className: \"w-5 h-5 text-green-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"Completados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: estadisticas.temasCompletados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiTrendingUp, {\n                                            className: \"w-5 h-5 text-purple-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-purple-800\",\n                                                    children: \"Progreso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: [\n                                                        estadisticas.porcentajeCompletado.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, undefined),\n                    estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Progreso del temario\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            estadisticas.porcentajeCompletado.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: \"\".concat(estadisticas.porcentajeCompletado, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined),\n            temario.tipo === 'completo' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-xl p-6 \".concat(tienePlanificacion ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                tienePlanificacion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                    className: \"w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiZap, {\n                                    className: \"w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2 \".concat(tienePlanificacion ? 'text-green-900' : 'text-blue-900'),\n                                            children: tienePlanificacion ? 'Planificación Configurada' : 'Planificación Inteligente con IA'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mb-3 \".concat(tienePlanificacion ? 'text-green-800' : 'text-blue-800'),\n                                            children: tienePlanificacion ? 'Ya tienes configurada tu planificación de estudio personalizada. Pronto podrás ver tu calendario y seguimiento.' : 'Configura tu planificación personalizada con nuestro asistente inteligente:'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-blue-800 text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Planificaci\\xf3n autom\\xe1tica de estudio con IA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Seguimiento de progreso personalizado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Recomendaciones de orden de estudio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Estimaci\\xf3n de tiempos por tema\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: !tienePlanificacion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleIniciarPlanificacion,\n                                className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiZap, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Configurar Planificaci\\xf3n\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModificarPlanificacion,\n                                className: \"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiSettings, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Modificar Planificaci\\xf3n\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Temas del Temario\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Marca los temas como completados seg\\xfan vayas estudi\\xe1ndolos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: temas.map((tema)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium\",\n                                                        children: tema.numero\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium \".concat(tema.completado ? 'text-gray-500 line-through' : 'text-gray-900'),\n                                                            children: tema.titulo\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        tema.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: tema.descripcion\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        tema.fecha_completado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2 text-sm text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                                                    className: \"w-4 h-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"Completado el \",\n                                                                formatearFecha(tema.fecha_completado)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemaActions__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                tema: tema,\n                                                onEdit: handleEditarTema,\n                                                onDelete: handleEliminarTema,\n                                                onToggleCompletado: handleToggleCompletado,\n                                                isUpdating: actualizandoTema === tema.id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tema.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, undefined),\n            temario && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemarioEditModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: mostrarModalEdicion,\n                onClose: handleCancelarEdicion,\n                temario: temario,\n                onSave: handleGuardarTemario\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 456,\n                columnNumber: 9\n            }, undefined),\n            temaSeleccionado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemaEditModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: mostrarModalEdicionTema,\n                onClose: handleCancelarEdicionTema,\n                tema: temaSeleccionado,\n                onSave: handleGuardarTema\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 466,\n                columnNumber: 9\n            }, undefined),\n            mostrarConfirmacionEliminar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiTrash2, {\n                                        className: \"w-6 h-6 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Eliminar Temario\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"\\xbfEst\\xe1s seguro de que quieres eliminar este temario? Esta acci\\xf3n:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-red-600 space-y-1 ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                '• Eliminar\\xe1 permanentemente el temario \"',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: temario === null || temario === void 0 ? void 0 : temario.titulo\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 61\n                                                }, undefined),\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Eliminar\\xe1 todos los temas asociados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Eliminar\\xe1 la planificaci\\xf3n de estudios configurada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Eliminar\\xe1 todos los planes de estudios generados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-3 font-medium\",\n                                    children: \"Esta acci\\xf3n no se puede deshacer.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancelarEliminarTemario,\n                                    disabled: eliminandoTemario,\n                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleConfirmarEliminarTemario,\n                                    disabled: eliminandoTemario,\n                                    className: \"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center\",\n                                    children: eliminandoTemario ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Eliminando...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiTrash2, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Eliminar Temario\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 476,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemarioManager, \"IMwz7Otk8TD65pRu7+3XZOgxmnE=\");\n_c = TemarioManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemarioManager);\nvar _c;\n$RefreshReg$(_c, \"TemarioManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\n"));

/***/ })

});