"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx":
/*!************************************************************!*\
  !*** ./src/features/temario/components/TemarioManager.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiEdit,FiSettings,FiTrendingUp,FiZap!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _features_planificacion_components_PlanificacionAsistente__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/planificacion/components/PlanificacionAsistente */ \"(app-pages-browser)/./src/features/planificacion/components/PlanificacionAsistente.tsx\");\n/* harmony import */ var _TemarioEditModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TemarioEditModal */ \"(app-pages-browser)/./src/features/temario/components/TemarioEditModal.tsx\");\n/* harmony import */ var _TemaEditModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemaEditModal */ \"(app-pages-browser)/./src/features/temario/components/TemaEditModal.tsx\");\n/* harmony import */ var _TemaActions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TemaActions */ \"(app-pages-browser)/./src/features/temario/components/TemaActions.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TemarioManager = ()=>{\n    _s();\n    const [temario, setTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temas, setTemas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [actualizandoTema, setActualizandoTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarAsistentePlanificacion, setMostrarAsistentePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarModalEdicion, setMostrarModalEdicion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarModalEdicionTema, setMostrarModalEdicionTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [temaSeleccionado, setTemaSeleccionado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TemarioManager.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"TemarioManager.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            const temarioData = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemarioUsuario)();\n            if (temarioData) {\n                setTemario(temarioData);\n                const [temasData, estadisticasData, planificacionConfigurada] = await Promise.all([\n                    (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemas)(temarioData.id),\n                    (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temarioData.id),\n                    (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.tienePlanificacionConfigurada)(temarioData.id)\n                ]);\n                setTemas(temasData);\n                setEstadisticas(estadisticasData);\n                setTienePlanificacion(planificacionConfigurada);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del temario:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al cargar el temario');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleToggleCompletado = async (temaId, completado)=>{\n        setActualizandoTema(temaId);\n        try {\n            const exito = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.actualizarEstadoTema)(temaId, !completado);\n            if (exito) {\n                // Actualizar el estado local\n                setTemas(temas.map((tema)=>tema.id === temaId ? {\n                        ...tema,\n                        completado: !completado,\n                        fecha_completado: !completado ? new Date().toISOString() : undefined\n                    } : tema));\n                // Recalcular estadísticas\n                if (temario) {\n                    const nuevasEstadisticas = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temario.id);\n                    setEstadisticas(nuevasEstadisticas);\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success(!completado ? 'Tema marcado como completado' : 'Tema marcado como pendiente');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al actualizar el estado del tema');\n            }\n        } catch (error) {\n            console.error('Error al actualizar tema:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al actualizar el tema');\n        } finally{\n            setActualizandoTema(null);\n        }\n    };\n    const formatearFecha = (fecha)=>{\n        return new Date(fecha).toLocaleDateString('es-ES', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric'\n        });\n    };\n    const handleIniciarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(true);\n    };\n    const handleModificarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(true);\n    };\n    const handleCompletarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(false);\n        setTienePlanificacion(true);\n        // Recargar datos para reflejar los cambios\n        cargarDatos();\n    };\n    const handleCancelarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(false);\n    };\n    const handleEditarTemario = ()=>{\n        setMostrarModalEdicion(true);\n    };\n    const handleGuardarTemario = (temarioActualizado)=>{\n        setTemario(temarioActualizado);\n        setMostrarModalEdicion(false);\n    };\n    const handleCancelarEdicion = ()=>{\n        setMostrarModalEdicion(false);\n    };\n    const handleEditarTema = (tema)=>{\n        setTemaSeleccionado(tema);\n        setMostrarModalEdicionTema(true);\n    };\n    const handleGuardarTema = (temaActualizado)=>{\n        setTemas(temas.map((tema)=>tema.id === temaActualizado.id ? temaActualizado : tema));\n        setMostrarModalEdicionTema(false);\n        setTemaSeleccionado(null);\n    };\n    const handleCancelarEdicionTema = ()=>{\n        setMostrarModalEdicionTema(false);\n        setTemaSeleccionado(null);\n    };\n    const handleEliminarTema = async (temaId)=>{\n        // Eliminar tema del estado local\n        setTemas(temas.filter((tema)=>tema.id !== temaId));\n        // Recalcular estadísticas\n        if (temario) {\n            const nuevasEstadisticas = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temario.id);\n            setEstadisticas(nuevasEstadisticas);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!temario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiBook, {\n                    className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No hay temario configurado\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"Configura tu temario desde el dashboard para comenzar.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar asistente de planificación si está activo\n    if (mostrarAsistentePlanificacion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanificacionAsistente__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            temario: temario,\n            onComplete: handleCompletarPlanificacion,\n            onCancel: handleCancelarPlanificacion,\n            isEditing: tienePlanificacion\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: temario.titulo\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    temario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: temario.descripcion\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(temario.tipo === 'completo' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'),\n                                                children: temario.tipo === 'completo' ? 'Temario Completo' : 'Temas Sueltos'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"Creado el \",\n                                                    formatearFecha(temario.creado_en)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleEditarTemario,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors\",\n                                    title: \"Editar temario\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiEdit, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiBook, {\n                                            className: \"w-5 h-5 text-blue-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Total Temas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: estadisticas.totalTemas\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                            className: \"w-5 h-5 text-green-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"Completados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: estadisticas.temasCompletados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiTrendingUp, {\n                                            className: \"w-5 h-5 text-purple-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-purple-800\",\n                                                    children: \"Progreso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: [\n                                                        estadisticas.porcentajeCompletado.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined),\n                    estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Progreso del temario\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            estadisticas.porcentajeCompletado.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: \"\".concat(estadisticas.porcentajeCompletado, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            temario.tipo === 'completo' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-xl p-6 \".concat(tienePlanificacion ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                tienePlanificacion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                    className: \"w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiZap, {\n                                    className: \"w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2 \".concat(tienePlanificacion ? 'text-green-900' : 'text-blue-900'),\n                                            children: tienePlanificacion ? 'Planificación Configurada' : 'Planificación Inteligente con IA'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mb-3 \".concat(tienePlanificacion ? 'text-green-800' : 'text-blue-800'),\n                                            children: tienePlanificacion ? 'Ya tienes configurada tu planificación de estudio personalizada. Pronto podrás ver tu calendario y seguimiento.' : 'Configura tu planificación personalizada con nuestro asistente inteligente:'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-blue-800 text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Planificaci\\xf3n autom\\xe1tica de estudio con IA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Seguimiento de progreso personalizado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Recomendaciones de orden de estudio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Estimaci\\xf3n de tiempos por tema\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: !tienePlanificacion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleIniciarPlanificacion,\n                                className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiZap, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Configurar Planificaci\\xf3n\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModificarPlanificacion,\n                                className: \"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiSettings, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Modificar Planificaci\\xf3n\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Temas del Temario\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Marca los temas como completados seg\\xfan vayas estudi\\xe1ndolos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: temas.map((tema)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium\",\n                                                        children: tema.numero\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium \".concat(tema.completado ? 'text-gray-500 line-through' : 'text-gray-900'),\n                                                            children: tema.titulo\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        tema.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: tema.descripcion\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        tema.fecha_completado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2 text-sm text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                                                    className: \"w-4 h-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"Completado el \",\n                                                                formatearFecha(tema.fecha_completado)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemaActions__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                tema: tema,\n                                                onEdit: handleEditarTema,\n                                                onDelete: handleEliminarTema,\n                                                onToggleCompletado: handleToggleCompletado,\n                                                isUpdating: actualizandoTema === tema.id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tema.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined),\n            temario && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemarioEditModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: mostrarModalEdicion,\n                onClose: handleCancelarEdicion,\n                temario: temario,\n                onSave: handleGuardarTemario\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, undefined),\n            temaSeleccionado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemaEditModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: mostrarModalEdicionTema,\n                onClose: handleCancelarEdicionTema,\n                tema: temaSeleccionado,\n                onSave: handleGuardarTema\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 405,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemarioManager, \"Jx9P86PceqgdNawpu1osiXuX0v4=\");\n_c = TemarioManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemarioManager);\nvar _c;\n$RefreshReg$(_c, \"TemarioManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\n"));

/***/ })

});