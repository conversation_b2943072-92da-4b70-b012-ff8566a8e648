"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx":
/*!****************************************************************!*\
  !*** ./src/features/flashcards/components/FlashcardViewer.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashcardViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardCollectionView */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardCollectionView.tsx\");\n/* harmony import */ var _FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyOptions */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardStudyOptions.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardStudyMode.tsx\");\n/* harmony import */ var _FlashcardEditModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FlashcardEditModal */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardEditModal.tsx\");\n/* harmony import */ var _FlashcardGeneralStatistics__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./FlashcardGeneralStatistics */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGeneralStatistics.tsx\");\n/* harmony import */ var _dashboard_components_StudyStatistics__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../dashboard/components/StudyStatistics */ \"(app-pages-browser)/./src/features/dashboard/components/StudyStatistics.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction FlashcardViewer() {\n    _s();\n    // Estados principales\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingColecciones, setIsLoadingColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modales y modos\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarOpcionesEstudio, setMostrarOpcionesEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticas, setMostrarEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticasGenerales, setMostrarEstadisticasGenerales] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEditarFlashcard, setMostrarEditarFlashcard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [flashcardParaEditar, setFlashcardParaEditar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingFlashcardId, setDeletingFlashcardId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoadingColecciones(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoadingColecciones(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const iniciarModoEstudio = async function() {\n        let tipoEstudio = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'programadas';\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                let flashcardsParaEstudiar = [];\n                // Obtener flashcards según el tipo de estudio seleccionado\n                switch(tipoEstudio){\n                    case 'programadas':\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                        break;\n                    case 'dificiles':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsMasDificiles)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'aleatorias':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsAleatorias)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'no-recientes':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsNoRecientes)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'nuevas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'nuevo', 20);\n                        break;\n                    case 'aprendiendo':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendiendo', 20);\n                        break;\n                    case 'repasando':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'repasando', 20);\n                        break;\n                    case 'aprendidas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendido', 20);\n                        break;\n                    default:\n                        const defaultData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = defaultData.filter((flashcard)=>flashcard.debeEstudiar);\n                }\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Si no hay flashcards para el tipo seleccionado, mostrar mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (tipoEstudio === 'programadas') {\n                        alert('No hay flashcards programadas para estudiar hoy. Puedes usar \"Opciones de estudio\" para elegir otro tipo de repaso.');\n                        return;\n                    } else {\n                        alert(\"No hay flashcards disponibles para el tipo de estudio seleccionado.\");\n                        return;\n                    }\n                }\n                // Usar las flashcards obtenidas\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarOpcionesEstudio(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo estudio:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const salirModoEstudio = async ()=>{\n        setModoEstudio(false);\n        // Recargar las flashcards y estadísticas\n        if (coleccionSeleccionada) {\n            try {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const ordenadas = [\n                    ...data\n                ].sort((a, b)=>{\n                    if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                    if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                    return 0;\n                });\n                setFlashcards(ordenadas);\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n            } catch (error) {\n                console.error('Error al recargar datos:', error);\n            }\n        }\n    };\n    const handleRespuesta = async (dificultad)=>{\n        if (!flashcards[activeIndex]) return;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcards[activeIndex].id, dificultad);\n            if (!exito) {\n                throw new Error('Error al registrar la respuesta');\n            }\n            // Si es la última tarjeta, terminar la sesión\n            if (activeIndex >= flashcards.length - 1) {\n                alert('¡Has completado la sesión de estudio!');\n                await salirModoEstudio();\n            } else {\n                // Avanzar a la siguiente tarjeta\n                setActiveIndex(activeIndex + 1);\n            }\n        } catch (error) {\n            console.error('Error al actualizar progreso:', error);\n            setError('Error al guardar tu respuesta. Por favor, inténtalo de nuevo.');\n        } finally{\n            setRespondiendo(false);\n        }\n    };\n    const handleNavigate = (direction)=>{\n        if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n        } else if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n        }\n    };\n    const handleEliminarColeccion = async (coleccionId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta colección? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingId(coleccionId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (exito) {\n                // Recargar las colecciones\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                setColecciones(data);\n                // Si la colección eliminada era la seleccionada, deseleccionarla\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                setError('No se pudo eliminar la colección');\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            setError('Error al eliminar la colección');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardParaEditar(flashcard);\n        setMostrarEditarFlashcard(true);\n    };\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta flashcard? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingFlashcardId(flashcardId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (exito) {\n                // Recargar las flashcards de la colección actual\n                if (coleccionSeleccionada) {\n                    const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                    // Actualizar estadísticas\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                setError('No se pudo eliminar la flashcard');\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            setError('Error al eliminar la flashcard');\n        } finally{\n            setDeletingFlashcardId(null);\n        }\n    };\n    const handleGuardarFlashcard = async (flashcardActualizada)=>{\n        // Actualizar la flashcard en el estado local\n        setFlashcards((prevFlashcards)=>prevFlashcards.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: salirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Mis Flashcards\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMostrarEstadisticasGenerales(true),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart2, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Estad\\xedsticas Generales\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this),\n                    !coleccionSeleccionada ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: isLoadingColecciones,\n                        deletingId: deletingId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setColeccionSeleccionada(null),\n                                className: \"mb-4 text-blue-600 hover:text-blue-800 flex items-center\",\n                                children: \"← Volver a mis colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                coleccion: coleccionSeleccionada,\n                                flashcards: flashcards,\n                                estadisticas: estadisticas,\n                                isLoading: isLoading,\n                                onStartStudy: ()=>iniciarModoEstudio('programadas'),\n                                onShowStudyOptions: ()=>setMostrarOpcionesEstudio(true),\n                                onShowStatistics: ()=>setMostrarEstadisticas(true),\n                                onEditFlashcard: handleEditarFlashcard,\n                                onDeleteFlashcard: handleEliminarFlashcard,\n                                deletingFlashcardId: deletingFlashcardId\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: mostrarOpcionesEstudio,\n                onClose: ()=>setMostrarOpcionesEstudio(false),\n                onSelectStudyType: iniciarModoEstudio,\n                estadisticas: estadisticas,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            mostrarEstadisticas && coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dashboard_components_StudyStatistics__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                coleccionId: coleccionSeleccionada.id,\n                onClose: ()=>setMostrarEstadisticas(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            mostrarEstadisticasGenerales && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardGeneralStatistics__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>setMostrarEstadisticasGenerales(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this),\n            flashcardParaEditar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardEditModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                flashcard: flashcardParaEditar,\n                isOpen: mostrarEditarFlashcard,\n                onClose: ()=>{\n                    setMostrarEditarFlashcard(false);\n                    setFlashcardParaEditar(null);\n                },\n                onSave: handleGuardarFlashcard\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n        lineNumber: 305,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardViewer, \"omgmcgMuCAZRp5VCnCl8CFZhiiM=\");\n_c = FlashcardViewer;\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\n"));

/***/ })

});