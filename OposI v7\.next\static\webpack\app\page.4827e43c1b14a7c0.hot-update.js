"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/conversacionesService.ts":
/*!***************************************************!*\
  !*** ./src/lib/supabase/conversacionesService.ts ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarConversacion: () => (/* binding */ activarConversacion),\n/* harmony export */   actualizarConversacion: () => (/* binding */ actualizarConversacion),\n/* harmony export */   crearConversacion: () => (/* binding */ crearConversacion),\n/* harmony export */   desactivarTodasLasConversaciones: () => (/* binding */ desactivarTodasLasConversaciones),\n/* harmony export */   eliminarConversacion: () => (/* binding */ eliminarConversacion),\n/* harmony export */   guardarMensaje: () => (/* binding */ guardarMensaje),\n/* harmony export */   obtenerConversacionActiva: () => (/* binding */ obtenerConversacionActiva),\n/* harmony export */   obtenerConversacionPorId: () => (/* binding */ obtenerConversacionPorId),\n/* harmony export */   obtenerConversaciones: () => (/* binding */ obtenerConversaciones),\n/* harmony export */   obtenerMensajesPorConversacionId: () => (/* binding */ obtenerMensajesPorConversacionId)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\n * Crea una nueva conversación\n * @param titulo Título de la conversación\n * @param activa Si la conversación debe marcarse como activa\n */ async function crearConversacion(titulo) {\n    let activa = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { data: { user } } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            console.error('No hay usuario autenticado para crear conversación');\n            return null;\n        }\n        // Si la conversación va a ser activa, primero desactivamos todas las demás\n        if (activa) {\n            await desactivarTodasLasConversaciones();\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').insert([\n            {\n                titulo,\n                activa,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear conversación:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error inesperado al crear conversación:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las conversaciones ordenadas por fecha de actualización\n */ async function obtenerConversaciones() {\n    try {\n        // Obtener el usuario actual\n        const { data: { user } } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            console.error('No hay usuario autenticado para obtener conversaciones');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').select('*').eq('user_id', user.id).order('actualizado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener conversaciones:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error inesperado al obtener conversaciones:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una conversación específica por su ID\n */ async function obtenerConversacionPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener conversación:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Actualiza el título de una conversación\n */ async function actualizarConversacion(id, titulo) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').update({\n        titulo,\n        actualizado_en: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error al actualizar conversación:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Marca una conversación como activa y desactiva todas las demás\n */ async function activarConversacion(id) {\n    try {\n        // Primero desactivamos todas las conversaciones\n        await desactivarTodasLasConversaciones();\n        // Luego activamos la conversación específica\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').update({\n            activa: true,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', id);\n        if (error) {\n            console.error('Error al activar conversación:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error inesperado al activar conversación:', error);\n        return false;\n    }\n}\n/**\n * Desactiva todas las conversaciones\n */ async function desactivarTodasLasConversaciones() {\n    try {\n        // Obtener el usuario actual\n        const { data: { user } } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            console.error('No hay usuario autenticado para desactivar conversaciones');\n            return false;\n        }\n        // Desactivar todas las conversaciones del usuario actual\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').update({\n            activa: false\n        }).eq('user_id', user.id).eq('activa', true); // Solo actualizar las que están activas\n        if (error) {\n            console.error('Error al desactivar todas las conversaciones:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error inesperado al desactivar conversaciones:', error);\n        return false;\n    }\n}\n/**\n * Obtiene la conversación activa actual\n */ async function obtenerConversacionActiva() {\n    try {\n        // Obtener el usuario actual\n        const { data: { user } } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            console.warn('No hay usuario autenticado para obtener conversación activa');\n            return null;\n        }\n        // Primero intentar obtener todas las conversaciones del usuario para debug\n        const { data: todasConversaciones, error: errorTodas } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').select('*').eq('user_id', user.id);\n        if (errorTodas) {\n            console.error('Error al obtener todas las conversaciones:', errorTodas);\n        }\n        // Ahora intentar obtener solo las activas\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').select('*').eq('user_id', user.id).eq('activa', true).limit(1);\n        if (error) {\n            console.error('Error al obtener conversación activa:', error);\n            // Si es un error 406, puede ser que no haya conversaciones activas\n            if (error.code === '406' || error.message.includes('406')) {\n                console.log('Error 406 - probablemente no hay conversaciones activas');\n                return null;\n            }\n            return null;\n        }\n        console.log('Conversaciones activas encontradas:', data);\n        // Si hay datos, devolver el primer elemento, si no, devolver null\n        return data && data.length > 0 ? data[0] : null;\n    } catch (error) {\n        console.error('Error inesperado al obtener conversación activa:', error);\n        return null;\n    }\n}\n/**\n * Guarda un mensaje en la base de datos\n */ async function guardarMensaje(mensaje) {\n    try {\n        var _data_;\n        // Primero verificamos que la conversación exista\n        const { data: conversacion, error: errorConversacion } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').select('id').eq('id', mensaje.conversacion_id).single();\n        if (errorConversacion) {\n            console.error('Error al verificar la conversación:', errorConversacion);\n            return null;\n        }\n        // Guardar el mensaje\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('mensajes').insert([\n            mensaje\n        ]).select();\n        if (error) {\n            console.error('Error al guardar mensaje:', error);\n            return null;\n        }\n        // Actualizar la fecha de la conversación\n        const { error: errorActualizacion } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').update({\n            actualizado_en: new Date().toISOString()\n        }).eq('id', mensaje.conversacion_id);\n        if (errorActualizacion) {\n            console.error('Error al actualizar la fecha de la conversación:', errorActualizacion);\n        // No retornamos error aquí porque el mensaje ya se guardó correctamente\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error inesperado al guardar mensaje:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los mensajes de una conversación\n */ async function obtenerMensajesPorConversacionId(conversacionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('mensajes').select('*').eq('conversacion_id', conversacionId).order('timestamp', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener mensajes:', error);\n        return [];\n    }\n    return data || [];\n}\n/**\n * Elimina una conversación y todos sus mensajes asociados\n */ async function eliminarConversacion(conversacionId) {\n    try {\n        // Obtener el usuario actual\n        const { data: { user } } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            console.error('No hay usuario autenticado para eliminar conversación');\n            return false;\n        }\n        // Primero eliminar todos los mensajes de la conversación\n        const { error: errorMensajes } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('mensajes').delete().eq('conversacion_id', conversacionId);\n        if (errorMensajes) {\n            console.error('Error al eliminar mensajes de la conversación:', errorMensajes);\n            return false;\n        }\n        // Luego eliminar la conversación\n        const { error: errorConversacion, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('conversaciones').delete({\n            count: 'exact'\n        }).eq('id', conversacionId).eq('user_id', user.id); // Asegurar que solo se eliminen conversaciones del usuario actual\n        if (errorConversacion) {\n            console.error('Error al eliminar conversación:', errorConversacion);\n            return false;\n        }\n        if (count === 0) {\n            console.warn('No se eliminó ninguna conversación. Posibles causas: conversación no existe o no pertenece al usuario');\n            return false;\n        }\n        console.log('Conversación eliminada exitosamente');\n        return true;\n    } catch (error) {\n        console.error('Error inesperado al eliminar conversación:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UvY29udmVyc2FjaW9uZXNTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQW1FO0FBRW5FOzs7O0NBSUMsR0FDTSxlQUFlQyxrQkFBa0JDLE1BQWM7UUFBRUMsU0FBQUEsaUVBQWtCO0lBQ3hFLElBQUk7WUErQktDO1FBOUJQLDRCQUE0QjtRQUM1QixNQUFNLEVBQUVBLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNTCxxREFBUUEsQ0FBQ00sSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNURyxRQUFRQyxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1Q7UUFJQSwyRUFBMkU7UUFDM0UsSUFBSU4sUUFBUTtZQUNWLE1BQU1PO1FBQ1I7UUFFQSxNQUFNLEVBQUVOLElBQUksRUFBRUssS0FBSyxFQUFFLEdBQUcsTUFBTVQscURBQVFBLENBQ25DVyxJQUFJLENBQUMsa0JBQ0xDLE1BQU0sQ0FBQztZQUFDO2dCQUNQVjtnQkFDQUM7Z0JBQ0FVLFNBQVNSLEtBQUtTLEVBQUU7WUFDbEI7U0FBRSxFQUNEQyxNQUFNO1FBRVQsSUFBSU4sT0FBTztZQUNURCxRQUFRQyxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5QyxPQUFPO1FBQ1Q7UUFHQSxPQUFPTCxDQUFBQSxpQkFBQUEsNEJBQUFBLFNBQUFBLElBQU0sQ0FBQyxFQUFFLGNBQVRBLDZCQUFBQSxPQUFXVSxFQUFFLEtBQUk7SUFDMUIsRUFBRSxPQUFPTCxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQywyQ0FBMkNBO1FBQ3pELE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFlTztJQUNwQixJQUFJO1FBQ0YsNEJBQTRCO1FBQzVCLE1BQU0sRUFBRVosTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1MLHFEQUFRQSxDQUFDTSxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1RHLFFBQVFDLEtBQUssQ0FBQztZQUNkLE9BQU8sRUFBRTtRQUNYO1FBRUEsTUFBTSxFQUFFTCxJQUFJLEVBQUVLLEtBQUssRUFBRSxHQUFHLE1BQU1ULHFEQUFRQSxDQUNuQ1csSUFBSSxDQUFDLGtCQUNMSSxNQUFNLENBQUMsS0FDUEUsRUFBRSxDQUFDLFdBQVdaLEtBQUtTLEVBQUUsRUFDckJJLEtBQUssQ0FBQyxrQkFBa0I7WUFBRUMsV0FBVztRQUFNO1FBRTlDLElBQUlWLE9BQU87WUFDVEQsUUFBUUMsS0FBSyxDQUFDLG9DQUFvQ0E7WUFDbEQsT0FBTyxFQUFFO1FBQ1g7UUFFQSxPQUFPTCxRQUFRLEVBQUU7SUFDbkIsRUFBRSxPQUFPSyxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQywrQ0FBK0NBO1FBQzdELE9BQU8sRUFBRTtJQUNYO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVXLHlCQUF5Qk4sRUFBVTtJQUN2RCxNQUFNLEVBQUVWLElBQUksRUFBRUssS0FBSyxFQUFFLEdBQUcsTUFBTVQscURBQVFBLENBQ25DVyxJQUFJLENBQUMsa0JBQ0xJLE1BQU0sQ0FBQyxLQUNQRSxFQUFFLENBQUMsTUFBTUgsSUFDVE8sTUFBTTtJQUVULElBQUlaLE9BQU87UUFDVEQsUUFBUUMsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsT0FBTztJQUNUO0lBRUEsT0FBT0w7QUFDVDtBQUVBOztDQUVDLEdBQ00sZUFBZWtCLHVCQUF1QlIsRUFBVSxFQUFFWixNQUFjO0lBQ3JFLE1BQU0sRUFBRU8sS0FBSyxFQUFFLEdBQUcsTUFBTVQscURBQVFBLENBQzdCVyxJQUFJLENBQUMsa0JBQ0xZLE1BQU0sQ0FBQztRQUFFckI7UUFBUXNCLGdCQUFnQixJQUFJQyxPQUFPQyxXQUFXO0lBQUcsR0FDMURULEVBQUUsQ0FBQyxNQUFNSDtJQUVaLElBQUlMLE9BQU87UUFDVEQsUUFBUUMsS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsT0FBTztJQUNUO0lBRUEsT0FBTztBQUNUO0FBRUE7O0NBRUMsR0FDTSxlQUFla0Isb0JBQW9CYixFQUFVO0lBQ2xELElBQUk7UUFDRixnREFBZ0Q7UUFDaEQsTUFBTUo7UUFFTiw2Q0FBNkM7UUFDN0MsTUFBTSxFQUFFRCxLQUFLLEVBQUUsR0FBRyxNQUFNVCxxREFBUUEsQ0FDN0JXLElBQUksQ0FBQyxrQkFDTFksTUFBTSxDQUFDO1lBQUVwQixRQUFRO1lBQU1xQixnQkFBZ0IsSUFBSUMsT0FBT0MsV0FBVztRQUFHLEdBQ2hFVCxFQUFFLENBQUMsTUFBTUg7UUFFWixJQUFJTCxPQUFPO1lBQ1RELFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hELE9BQU87UUFDVDtRQUVBLE9BQU87SUFDVCxFQUFFLE9BQU9BLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLDZDQUE2Q0E7UUFDM0QsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVDO0lBQ3BCLElBQUk7UUFDRiw0QkFBNEI7UUFDNUIsTUFBTSxFQUFFTixNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUwscURBQVFBLENBQUNNLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVEcsUUFBUUMsS0FBSyxDQUFDO1lBQ2QsT0FBTztRQUNUO1FBRUEseURBQXlEO1FBQ3pELE1BQU0sRUFBRUEsS0FBSyxFQUFFLEdBQUcsTUFBTVQscURBQVFBLENBQzdCVyxJQUFJLENBQUMsa0JBQ0xZLE1BQU0sQ0FBQztZQUFFcEIsUUFBUTtRQUFNLEdBQ3ZCYyxFQUFFLENBQUMsV0FBV1osS0FBS1MsRUFBRSxFQUNyQkcsRUFBRSxDQUFDLFVBQVUsT0FBTyx3Q0FBd0M7UUFFL0QsSUFBSVIsT0FBTztZQUNURCxRQUFRQyxLQUFLLENBQUMsaURBQWlEQTtZQUMvRCxPQUFPO1FBQ1Q7UUFFQSxPQUFPO0lBQ1QsRUFBRSxPQUFPQSxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyxrREFBa0RBO1FBQ2hFLE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFlbUI7SUFDcEIsSUFBSTtRQUNGLDRCQUE0QjtRQUM1QixNQUFNLEVBQUV4QixNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUwscURBQVFBLENBQUNNLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVEcsUUFBUXFCLElBQUksQ0FBQztZQUNiLE9BQU87UUFDVDtRQUVBLDJFQUEyRTtRQUMzRSxNQUFNLEVBQUV6QixNQUFNMEIsbUJBQW1CLEVBQUVyQixPQUFPc0IsVUFBVSxFQUFFLEdBQUcsTUFBTS9CLHFEQUFRQSxDQUNwRVcsSUFBSSxDQUFDLGtCQUNMSSxNQUFNLENBQUMsS0FDUEUsRUFBRSxDQUFDLFdBQVdaLEtBQUtTLEVBQUU7UUFFeEIsSUFBSWlCLFlBQVk7WUFDZHZCLFFBQVFDLEtBQUssQ0FBQyw4Q0FBOENzQjtRQUM5RDtRQUVBLDBDQUEwQztRQUMxQyxNQUFNLEVBQUUzQixJQUFJLEVBQUVLLEtBQUssRUFBRSxHQUFHLE1BQU1ULHFEQUFRQSxDQUNuQ1csSUFBSSxDQUFDLGtCQUNMSSxNQUFNLENBQUMsS0FDUEUsRUFBRSxDQUFDLFdBQVdaLEtBQUtTLEVBQUUsRUFDckJHLEVBQUUsQ0FBQyxVQUFVLE1BQ2JlLEtBQUssQ0FBQztRQUVULElBQUl2QixPQUFPO1lBQ1RELFFBQVFDLEtBQUssQ0FBQyx5Q0FBeUNBO1lBQ3ZELG1FQUFtRTtZQUNuRSxJQUFJQSxNQUFNd0IsSUFBSSxLQUFLLFNBQVN4QixNQUFNeUIsT0FBTyxDQUFDQyxRQUFRLENBQUMsUUFBUTtnQkFDekQzQixRQUFRNEIsR0FBRyxDQUFDO2dCQUNaLE9BQU87WUFDVDtZQUNBLE9BQU87UUFDVDtRQUVBNUIsUUFBUTRCLEdBQUcsQ0FBQyx1Q0FBdUNoQztRQUVuRCxrRUFBa0U7UUFDbEUsT0FBT0EsUUFBUUEsS0FBS2lDLE1BQU0sR0FBRyxJQUFJakMsSUFBSSxDQUFDLEVBQUUsR0FBRztJQUM3QyxFQUFFLE9BQU9LLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLG9EQUFvREE7UUFDbEUsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWU2QixlQUFlQyxPQUEwQztJQUM3RSxJQUFJO1lBbUNLbkM7UUFsQ1AsaURBQWlEO1FBQ2pELE1BQU0sRUFBRUEsTUFBTW9DLFlBQVksRUFBRS9CLE9BQU9nQyxpQkFBaUIsRUFBRSxHQUFHLE1BQU16QyxxREFBUUEsQ0FDcEVXLElBQUksQ0FBQyxrQkFDTEksTUFBTSxDQUFDLE1BQ1BFLEVBQUUsQ0FBQyxNQUFNc0IsUUFBUUcsZUFBZSxFQUNoQ3JCLE1BQU07UUFFVCxJQUFJb0IsbUJBQW1CO1lBQ3JCakMsUUFBUUMsS0FBSyxDQUFDLHVDQUF1Q2dDO1lBQ3JELE9BQU87UUFDVDtRQUVBLHFCQUFxQjtRQUNyQixNQUFNLEVBQUVyQyxJQUFJLEVBQUVLLEtBQUssRUFBRSxHQUFHLE1BQU1ULHFEQUFRQSxDQUNuQ1csSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBQztZQUFDMkI7U0FBUSxFQUNoQnhCLE1BQU07UUFFVCxJQUFJTixPQUFPO1lBQ1RELFFBQVFDLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDLE9BQU87UUFDVDtRQUVBLHlDQUF5QztRQUN6QyxNQUFNLEVBQUVBLE9BQU9rQyxrQkFBa0IsRUFBRSxHQUFHLE1BQU0zQyxxREFBUUEsQ0FDakRXLElBQUksQ0FBQyxrQkFDTFksTUFBTSxDQUFDO1lBQUVDLGdCQUFnQixJQUFJQyxPQUFPQyxXQUFXO1FBQUcsR0FDbERULEVBQUUsQ0FBQyxNQUFNc0IsUUFBUUcsZUFBZTtRQUVuQyxJQUFJQyxvQkFBb0I7WUFDdEJuQyxRQUFRQyxLQUFLLENBQUMsb0RBQW9Ea0M7UUFDbEUsd0VBQXdFO1FBQzFFO1FBRUEsT0FBT3ZDLENBQUFBLGlCQUFBQSw0QkFBQUEsU0FBQUEsSUFBTSxDQUFDLEVBQUUsY0FBVEEsNkJBQUFBLE9BQVdVLEVBQUUsS0FBSTtJQUMxQixFQUFFLE9BQU9MLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLHdDQUF3Q0E7UUFDdEQsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVtQyxpQ0FBaUNDLGNBQXNCO0lBQzNFLE1BQU0sRUFBRXpDLElBQUksRUFBRUssS0FBSyxFQUFFLEdBQUcsTUFBTVQscURBQVFBLENBQ25DVyxJQUFJLENBQUMsWUFDTEksTUFBTSxDQUFDLEtBQ1BFLEVBQUUsQ0FBQyxtQkFBbUI0QixnQkFDdEIzQixLQUFLLENBQUMsYUFBYTtRQUFFQyxXQUFXO0lBQUs7SUFFeEMsSUFBSVYsT0FBTztRQUNURCxRQUFRQyxLQUFLLENBQUMsOEJBQThCQTtRQUM1QyxPQUFPLEVBQUU7SUFDWDtJQUVBLE9BQU9MLFFBQVEsRUFBRTtBQUNuQjtBQUVBOztDQUVDLEdBQ00sZUFBZTBDLHFCQUFxQkQsY0FBc0I7SUFDL0QsSUFBSTtRQUNGLDRCQUE0QjtRQUM1QixNQUFNLEVBQUV6QyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUwscURBQVFBLENBQUNNLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVEcsUUFBUUMsS0FBSyxDQUFDO1lBQ2QsT0FBTztRQUNUO1FBRUEseURBQXlEO1FBQ3pELE1BQU0sRUFBRUEsT0FBT3NDLGFBQWEsRUFBRSxHQUFHLE1BQU0vQyxxREFBUUEsQ0FDNUNXLElBQUksQ0FBQyxZQUNMcUMsTUFBTSxHQUNOL0IsRUFBRSxDQUFDLG1CQUFtQjRCO1FBRXpCLElBQUlFLGVBQWU7WUFDakJ2QyxRQUFRQyxLQUFLLENBQUMsa0RBQWtEc0M7WUFDaEUsT0FBTztRQUNUO1FBRUEsaUNBQWlDO1FBQ2pDLE1BQU0sRUFBRXRDLE9BQU9nQyxpQkFBaUIsRUFBRVEsS0FBSyxFQUFFLEdBQUcsTUFBTWpELHFEQUFRQSxDQUN2RFcsSUFBSSxDQUFDLGtCQUNMcUMsTUFBTSxDQUFDO1lBQUVDLE9BQU87UUFBUSxHQUN4QmhDLEVBQUUsQ0FBQyxNQUFNNEIsZ0JBQ1Q1QixFQUFFLENBQUMsV0FBV1osS0FBS1MsRUFBRSxHQUFHLGtFQUFrRTtRQUU3RixJQUFJMkIsbUJBQW1CO1lBQ3JCakMsUUFBUUMsS0FBSyxDQUFDLG1DQUFtQ2dDO1lBQ2pELE9BQU87UUFDVDtRQUVBLElBQUlRLFVBQVUsR0FBRztZQUNmekMsUUFBUXFCLElBQUksQ0FBQztZQUNiLE9BQU87UUFDVDtRQUVBckIsUUFBUTRCLEdBQUcsQ0FBQztRQUNaLE9BQU87SUFDVCxFQUFFLE9BQU8zQixPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyw4Q0FBOENBO1FBQzVELE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXHN1cGFiYXNlXFxjb252ZXJzYWNpb25lc1NlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3VwYWJhc2UsIENvbnZlcnNhY2lvbiwgTWVuc2FqZSB9IGZyb20gJy4vc3VwYWJhc2VDbGllbnQnO1xuXG4vKipcbiAqIENyZWEgdW5hIG51ZXZhIGNvbnZlcnNhY2nDs25cbiAqIEBwYXJhbSB0aXR1bG8gVMOtdHVsbyBkZSBsYSBjb252ZXJzYWNpw7NuXG4gKiBAcGFyYW0gYWN0aXZhIFNpIGxhIGNvbnZlcnNhY2nDs24gZGViZSBtYXJjYXJzZSBjb21vIGFjdGl2YVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXJDb252ZXJzYWNpb24odGl0dWxvOiBzdHJpbmcsIGFjdGl2YTogYm9vbGVhbiA9IGZhbHNlKTogUHJvbWlzZTxzdHJpbmcgfCBudWxsPiB7XG4gIHRyeSB7XG4gICAgLy8gT2J0ZW5lciBlbCB1c3VhcmlvIGFjdHVhbFxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdObyBoYXkgdXN1YXJpbyBhdXRlbnRpY2FkbyBwYXJhIGNyZWFyIGNvbnZlcnNhY2nDs24nKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuXG5cbiAgICAvLyBTaSBsYSBjb252ZXJzYWNpw7NuIHZhIGEgc2VyIGFjdGl2YSwgcHJpbWVybyBkZXNhY3RpdmFtb3MgdG9kYXMgbGFzIGRlbcOhc1xuICAgIGlmIChhY3RpdmEpIHtcbiAgICAgIGF3YWl0IGRlc2FjdGl2YXJUb2Rhc0xhc0NvbnZlcnNhY2lvbmVzKCk7XG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb252ZXJzYWNpb25lcycpXG4gICAgICAuaW5zZXJ0KFt7XG4gICAgICAgIHRpdHVsbyxcbiAgICAgICAgYWN0aXZhLFxuICAgICAgICB1c2VyX2lkOiB1c2VyLmlkXG4gICAgICB9XSlcbiAgICAgIC5zZWxlY3QoKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgY3JlYXIgY29udmVyc2FjacOzbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cblxuICAgIHJldHVybiBkYXRhPy5bMF0/LmlkIHx8IG51bGw7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW5lc3BlcmFkbyBhbCBjcmVhciBjb252ZXJzYWNpw7NuOicsIGVycm9yKTtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxufVxuXG4vKipcbiAqIE9idGllbmUgdG9kYXMgbGFzIGNvbnZlcnNhY2lvbmVzIG9yZGVuYWRhcyBwb3IgZmVjaGEgZGUgYWN0dWFsaXphY2nDs25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG9idGVuZXJDb252ZXJzYWNpb25lcygpOiBQcm9taXNlPENvbnZlcnNhY2lvbltdPiB7XG4gIHRyeSB7XG4gICAgLy8gT2J0ZW5lciBlbCB1c3VhcmlvIGFjdHVhbFxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdObyBoYXkgdXN1YXJpbyBhdXRlbnRpY2FkbyBwYXJhIG9idGVuZXIgY29udmVyc2FjaW9uZXMnKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnZlcnNhY2lvbmVzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5vcmRlcignYWN0dWFsaXphZG9fZW4nLCB7IGFzY2VuZGluZzogZmFsc2UgfSk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIG9idGVuZXIgY29udmVyc2FjaW9uZXM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhIHx8IFtdO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluZXNwZXJhZG8gYWwgb2J0ZW5lciBjb252ZXJzYWNpb25lczonLCBlcnJvcik7XG4gICAgcmV0dXJuIFtdO1xuICB9XG59XG5cbi8qKlxuICogT2J0aWVuZSB1bmEgY29udmVyc2FjacOzbiBlc3BlY8OtZmljYSBwb3Igc3UgSURcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG9idGVuZXJDb252ZXJzYWNpb25Qb3JJZChpZDogc3RyaW5nKTogUHJvbWlzZTxDb252ZXJzYWNpb24gfCBudWxsPiB7XG4gIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgLmZyb20oJ2NvbnZlcnNhY2lvbmVzJylcbiAgICAuc2VsZWN0KCcqJylcbiAgICAuZXEoJ2lkJywgaWQpXG4gICAgLnNpbmdsZSgpO1xuXG4gIGlmIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIG9idGVuZXIgY29udmVyc2FjacOzbjonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICByZXR1cm4gZGF0YTtcbn1cblxuLyoqXG4gKiBBY3R1YWxpemEgZWwgdMOtdHVsbyBkZSB1bmEgY29udmVyc2FjacOzblxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gYWN0dWFsaXphckNvbnZlcnNhY2lvbihpZDogc3RyaW5nLCB0aXR1bG86IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKCdjb252ZXJzYWNpb25lcycpXG4gICAgLnVwZGF0ZSh7IHRpdHVsbywgYWN0dWFsaXphZG9fZW46IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSB9KVxuICAgIC5lcSgnaWQnLCBpZCk7XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgYWN0dWFsaXphciBjb252ZXJzYWNpw7NuOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICByZXR1cm4gdHJ1ZTtcbn1cblxuLyoqXG4gKiBNYXJjYSB1bmEgY29udmVyc2FjacOzbiBjb21vIGFjdGl2YSB5IGRlc2FjdGl2YSB0b2RhcyBsYXMgZGVtw6FzXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhY3RpdmFyQ29udmVyc2FjaW9uKGlkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgdHJ5IHtcbiAgICAvLyBQcmltZXJvIGRlc2FjdGl2YW1vcyB0b2RhcyBsYXMgY29udmVyc2FjaW9uZXNcbiAgICBhd2FpdCBkZXNhY3RpdmFyVG9kYXNMYXNDb252ZXJzYWNpb25lcygpO1xuXG4gICAgLy8gTHVlZ28gYWN0aXZhbW9zIGxhIGNvbnZlcnNhY2nDs24gZXNwZWPDrWZpY2FcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnZlcnNhY2lvbmVzJylcbiAgICAgIC51cGRhdGUoeyBhY3RpdmE6IHRydWUsIGFjdHVhbGl6YWRvX2VuOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkgfSlcbiAgICAgIC5lcSgnaWQnLCBpZCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGFjdGl2YXIgY29udmVyc2FjacOzbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgcmV0dXJuIHRydWU7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW5lc3BlcmFkbyBhbCBhY3RpdmFyIGNvbnZlcnNhY2nDs246JywgZXJyb3IpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuXG4vKipcbiAqIERlc2FjdGl2YSB0b2RhcyBsYXMgY29udmVyc2FjaW9uZXNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGRlc2FjdGl2YXJUb2Rhc0xhc0NvbnZlcnNhY2lvbmVzKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICB0cnkge1xuICAgIC8vIE9idGVuZXIgZWwgdXN1YXJpbyBhY3R1YWxcbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcblxuICAgIGlmICghdXNlcikge1xuICAgICAgY29uc29sZS5lcnJvcignTm8gaGF5IHVzdWFyaW8gYXV0ZW50aWNhZG8gcGFyYSBkZXNhY3RpdmFyIGNvbnZlcnNhY2lvbmVzJyk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgLy8gRGVzYWN0aXZhciB0b2RhcyBsYXMgY29udmVyc2FjaW9uZXMgZGVsIHVzdWFyaW8gYWN0dWFsXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb252ZXJzYWNpb25lcycpXG4gICAgICAudXBkYXRlKHsgYWN0aXZhOiBmYWxzZSB9KVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5lcSgnYWN0aXZhJywgdHJ1ZSk7IC8vIFNvbG8gYWN0dWFsaXphciBsYXMgcXVlIGVzdMOhbiBhY3RpdmFzXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGRlc2FjdGl2YXIgdG9kYXMgbGFzIGNvbnZlcnNhY2lvbmVzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbmVzcGVyYWRvIGFsIGRlc2FjdGl2YXIgY29udmVyc2FjaW9uZXM6JywgZXJyb3IpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuXG4vKipcbiAqIE9idGllbmUgbGEgY29udmVyc2FjacOzbiBhY3RpdmEgYWN0dWFsXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBvYnRlbmVyQ29udmVyc2FjaW9uQWN0aXZhKCk6IFByb21pc2U8Q29udmVyc2FjaW9uIHwgbnVsbD4ge1xuICB0cnkge1xuICAgIC8vIE9idGVuZXIgZWwgdXN1YXJpbyBhY3R1YWxcbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcblxuICAgIGlmICghdXNlcikge1xuICAgICAgY29uc29sZS53YXJuKCdObyBoYXkgdXN1YXJpbyBhdXRlbnRpY2FkbyBwYXJhIG9idGVuZXIgY29udmVyc2FjacOzbiBhY3RpdmEnKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIC8vIFByaW1lcm8gaW50ZW50YXIgb2J0ZW5lciB0b2RhcyBsYXMgY29udmVyc2FjaW9uZXMgZGVsIHVzdWFyaW8gcGFyYSBkZWJ1Z1xuICAgIGNvbnN0IHsgZGF0YTogdG9kYXNDb252ZXJzYWNpb25lcywgZXJyb3I6IGVycm9yVG9kYXMgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY29udmVyc2FjaW9uZXMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKTtcblxuICAgIGlmIChlcnJvclRvZGFzKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBvYnRlbmVyIHRvZGFzIGxhcyBjb252ZXJzYWNpb25lczonLCBlcnJvclRvZGFzKTtcbiAgICB9XG5cbiAgICAvLyBBaG9yYSBpbnRlbnRhciBvYnRlbmVyIHNvbG8gbGFzIGFjdGl2YXNcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnZlcnNhY2lvbmVzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5lcSgnYWN0aXZhJywgdHJ1ZSlcbiAgICAgIC5saW1pdCgxKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgb2J0ZW5lciBjb252ZXJzYWNpw7NuIGFjdGl2YTonLCBlcnJvcik7XG4gICAgICAvLyBTaSBlcyB1biBlcnJvciA0MDYsIHB1ZWRlIHNlciBxdWUgbm8gaGF5YSBjb252ZXJzYWNpb25lcyBhY3RpdmFzXG4gICAgICBpZiAoZXJyb3IuY29kZSA9PT0gJzQwNicgfHwgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnNDA2JykpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0Vycm9yIDQwNiAtIHByb2JhYmxlbWVudGUgbm8gaGF5IGNvbnZlcnNhY2lvbmVzIGFjdGl2YXMnKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnQ29udmVyc2FjaW9uZXMgYWN0aXZhcyBlbmNvbnRyYWRhczonLCBkYXRhKTtcblxuICAgIC8vIFNpIGhheSBkYXRvcywgZGV2b2x2ZXIgZWwgcHJpbWVyIGVsZW1lbnRvLCBzaSBubywgZGV2b2x2ZXIgbnVsbFxuICAgIHJldHVybiBkYXRhICYmIGRhdGEubGVuZ3RoID4gMCA/IGRhdGFbMF0gOiBudWxsO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluZXNwZXJhZG8gYWwgb2J0ZW5lciBjb252ZXJzYWNpw7NuIGFjdGl2YTonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuLyoqXG4gKiBHdWFyZGEgdW4gbWVuc2FqZSBlbiBsYSBiYXNlIGRlIGRhdG9zXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBndWFyZGFyTWVuc2FqZShtZW5zYWplOiBPbWl0PE1lbnNhamUsICdpZCcgfCAndGltZXN0YW1wJz4pOiBQcm9taXNlPHN0cmluZyB8IG51bGw+IHtcbiAgdHJ5IHtcbiAgICAvLyBQcmltZXJvIHZlcmlmaWNhbW9zIHF1ZSBsYSBjb252ZXJzYWNpw7NuIGV4aXN0YVxuICAgIGNvbnN0IHsgZGF0YTogY29udmVyc2FjaW9uLCBlcnJvcjogZXJyb3JDb252ZXJzYWNpb24gfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY29udmVyc2FjaW9uZXMnKVxuICAgICAgLnNlbGVjdCgnaWQnKVxuICAgICAgLmVxKCdpZCcsIG1lbnNhamUuY29udmVyc2FjaW9uX2lkKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKGVycm9yQ29udmVyc2FjaW9uKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCB2ZXJpZmljYXIgbGEgY29udmVyc2FjacOzbjonLCBlcnJvckNvbnZlcnNhY2lvbik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICAvLyBHdWFyZGFyIGVsIG1lbnNhamVcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ21lbnNhamVzJylcbiAgICAgIC5pbnNlcnQoW21lbnNhamVdKVxuICAgICAgLnNlbGVjdCgpO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBndWFyZGFyIG1lbnNhamU6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgLy8gQWN0dWFsaXphciBsYSBmZWNoYSBkZSBsYSBjb252ZXJzYWNpw7NuXG4gICAgY29uc3QgeyBlcnJvcjogZXJyb3JBY3R1YWxpemFjaW9uIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnZlcnNhY2lvbmVzJylcbiAgICAgIC51cGRhdGUoeyBhY3R1YWxpemFkb19lbjogbmV3IERhdGUoKS50b0lTT1N0cmluZygpIH0pXG4gICAgICAuZXEoJ2lkJywgbWVuc2FqZS5jb252ZXJzYWNpb25faWQpO1xuXG4gICAgaWYgKGVycm9yQWN0dWFsaXphY2lvbikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgYWN0dWFsaXphciBsYSBmZWNoYSBkZSBsYSBjb252ZXJzYWNpw7NuOicsIGVycm9yQWN0dWFsaXphY2lvbik7XG4gICAgICAvLyBObyByZXRvcm5hbW9zIGVycm9yIGFxdcOtIHBvcnF1ZSBlbCBtZW5zYWplIHlhIHNlIGd1YXJkw7MgY29ycmVjdGFtZW50ZVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhPy5bMF0/LmlkIHx8IG51bGw7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW5lc3BlcmFkbyBhbCBndWFyZGFyIG1lbnNhamU6JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbi8qKlxuICogT2J0aWVuZSB0b2RvcyBsb3MgbWVuc2FqZXMgZGUgdW5hIGNvbnZlcnNhY2nDs25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG9idGVuZXJNZW5zYWplc1BvckNvbnZlcnNhY2lvbklkKGNvbnZlcnNhY2lvbklkOiBzdHJpbmcpOiBQcm9taXNlPE1lbnNhamVbXT4ge1xuICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKCdtZW5zYWplcycpXG4gICAgLnNlbGVjdCgnKicpXG4gICAgLmVxKCdjb252ZXJzYWNpb25faWQnLCBjb252ZXJzYWNpb25JZClcbiAgICAub3JkZXIoJ3RpbWVzdGFtcCcsIHsgYXNjZW5kaW5nOiB0cnVlIH0pO1xuXG4gIGlmIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIG9idGVuZXIgbWVuc2FqZXM6JywgZXJyb3IpO1xuICAgIHJldHVybiBbXTtcbiAgfVxuXG4gIHJldHVybiBkYXRhIHx8IFtdO1xufVxuXG4vKipcbiAqIEVsaW1pbmEgdW5hIGNvbnZlcnNhY2nDs24geSB0b2RvcyBzdXMgbWVuc2FqZXMgYXNvY2lhZG9zXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBlbGltaW5hckNvbnZlcnNhY2lvbihjb252ZXJzYWNpb25JZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIHRyeSB7XG4gICAgLy8gT2J0ZW5lciBlbCB1c3VhcmlvIGFjdHVhbFxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdObyBoYXkgdXN1YXJpbyBhdXRlbnRpY2FkbyBwYXJhIGVsaW1pbmFyIGNvbnZlcnNhY2nDs24nKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICAvLyBQcmltZXJvIGVsaW1pbmFyIHRvZG9zIGxvcyBtZW5zYWplcyBkZSBsYSBjb252ZXJzYWNpw7NuXG4gICAgY29uc3QgeyBlcnJvcjogZXJyb3JNZW5zYWplcyB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdtZW5zYWplcycpXG4gICAgICAuZGVsZXRlKClcbiAgICAgIC5lcSgnY29udmVyc2FjaW9uX2lkJywgY29udmVyc2FjaW9uSWQpO1xuXG4gICAgaWYgKGVycm9yTWVuc2FqZXMpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGVsaW1pbmFyIG1lbnNhamVzIGRlIGxhIGNvbnZlcnNhY2nDs246JywgZXJyb3JNZW5zYWplcyk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgLy8gTHVlZ28gZWxpbWluYXIgbGEgY29udmVyc2FjacOzblxuICAgIGNvbnN0IHsgZXJyb3I6IGVycm9yQ29udmVyc2FjaW9uLCBjb3VudCB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb252ZXJzYWNpb25lcycpXG4gICAgICAuZGVsZXRlKHsgY291bnQ6ICdleGFjdCcgfSlcbiAgICAgIC5lcSgnaWQnLCBjb252ZXJzYWNpb25JZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpOyAvLyBBc2VndXJhciBxdWUgc29sbyBzZSBlbGltaW5lbiBjb252ZXJzYWNpb25lcyBkZWwgdXN1YXJpbyBhY3R1YWxcblxuICAgIGlmIChlcnJvckNvbnZlcnNhY2lvbikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgZWxpbWluYXIgY29udmVyc2FjacOzbjonLCBlcnJvckNvbnZlcnNhY2lvbik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgaWYgKGNvdW50ID09PSAwKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ05vIHNlIGVsaW1pbsOzIG5pbmd1bmEgY29udmVyc2FjacOzbi4gUG9zaWJsZXMgY2F1c2FzOiBjb252ZXJzYWNpw7NuIG5vIGV4aXN0ZSBvIG5vIHBlcnRlbmVjZSBhbCB1c3VhcmlvJyk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ0NvbnZlcnNhY2nDs24gZWxpbWluYWRhIGV4aXRvc2FtZW50ZScpO1xuICAgIHJldHVybiB0cnVlO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluZXNwZXJhZG8gYWwgZWxpbWluYXIgY29udmVyc2FjacOzbjonLCBlcnJvcik7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59XG4iXSwibmFtZXMiOlsic3VwYWJhc2UiLCJjcmVhckNvbnZlcnNhY2lvbiIsInRpdHVsbyIsImFjdGl2YSIsImRhdGEiLCJ1c2VyIiwiYXV0aCIsImdldFVzZXIiLCJjb25zb2xlIiwiZXJyb3IiLCJkZXNhY3RpdmFyVG9kYXNMYXNDb252ZXJzYWNpb25lcyIsImZyb20iLCJpbnNlcnQiLCJ1c2VyX2lkIiwiaWQiLCJzZWxlY3QiLCJvYnRlbmVyQ29udmVyc2FjaW9uZXMiLCJlcSIsIm9yZGVyIiwiYXNjZW5kaW5nIiwib2J0ZW5lckNvbnZlcnNhY2lvblBvcklkIiwic2luZ2xlIiwiYWN0dWFsaXphckNvbnZlcnNhY2lvbiIsInVwZGF0ZSIsImFjdHVhbGl6YWRvX2VuIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiYWN0aXZhckNvbnZlcnNhY2lvbiIsIm9idGVuZXJDb252ZXJzYWNpb25BY3RpdmEiLCJ3YXJuIiwidG9kYXNDb252ZXJzYWNpb25lcyIsImVycm9yVG9kYXMiLCJsaW1pdCIsImNvZGUiLCJtZXNzYWdlIiwiaW5jbHVkZXMiLCJsb2ciLCJsZW5ndGgiLCJndWFyZGFyTWVuc2FqZSIsIm1lbnNhamUiLCJjb252ZXJzYWNpb24iLCJlcnJvckNvbnZlcnNhY2lvbiIsImNvbnZlcnNhY2lvbl9pZCIsImVycm9yQWN0dWFsaXphY2lvbiIsIm9idGVuZXJNZW5zYWplc1BvckNvbnZlcnNhY2lvbklkIiwiY29udmVyc2FjaW9uSWQiLCJlbGltaW5hckNvbnZlcnNhY2lvbiIsImVycm9yTWVuc2FqZXMiLCJkZWxldGUiLCJjb3VudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/conversacionesService.ts\n"));

/***/ })

});