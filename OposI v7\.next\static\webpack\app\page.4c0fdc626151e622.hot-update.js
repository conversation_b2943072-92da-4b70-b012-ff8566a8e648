"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/temario/components/TemarioSetup.tsx":
/*!**********************************************************!*\
  !*** ./src/features/temario/components/TemarioSetup.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiDownload,FiPlus,FiTrash2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _services_temariosPredefinidosService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/temariosPredefinidosService */ \"(app-pages-browser)/./src/features/temario/services/temariosPredefinidosService.ts\");\n/* harmony import */ var _TemariosPredefinidosSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemariosPredefinidosSelector */ \"(app-pages-browser)/./src/features/temario/components/TemariosPredefinidosSelector.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst TemarioSetup = (param)=>{\n    let { onComplete } = param;\n    _s();\n    const [paso, setPaso] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('seleccion');\n    const [tipoSeleccionado, setTipoSeleccionado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tituloTemario, setTituloTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [descripcionTemario, setDescripcionTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [temas, setTemas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            numero: 1,\n            titulo: '',\n            descripcion: ''\n        }\n    ]);\n    const [temarioPredefinidoSeleccionado, setTemarioPredefinidoSeleccionado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleTipoSeleccion = (tipo)=>{\n        setTipoSeleccionado(tipo);\n        if (tipo === 'predefinido') {\n            setPaso('predefinidos');\n        } else {\n            setPaso('configuracion');\n        }\n    };\n    const handleTemarioPredefinidoSeleccionado = (temario)=>{\n        setTemarioPredefinidoSeleccionado(temario);\n        setPaso('configuracion');\n    };\n    const handleVolverDesdePredefinidos = ()=>{\n        setPaso('seleccion');\n        setTipoSeleccionado(null);\n    };\n    const agregarTema = ()=>{\n        const nuevoNumero = temas.length + 1;\n        setTemas([\n            ...temas,\n            {\n                numero: nuevoNumero,\n                titulo: '',\n                descripcion: ''\n            }\n        ]);\n    };\n    const eliminarTema = (index)=>{\n        if (temas.length > 1) {\n            const nuevosTemasTemp = temas.filter((_, i)=>i !== index);\n            // Reordenar números\n            const nuevosTemasReordenados = nuevosTemasTemp.map((tema, i)=>({\n                    ...tema,\n                    numero: i + 1\n                }));\n            setTemas(nuevosTemasReordenados);\n        }\n    };\n    const actualizarTema = (index, campo, valor)=>{\n        const nuevosTemasTemp = [\n            ...temas\n        ];\n        nuevosTemasTemp[index] = {\n            ...nuevosTemasTemp[index],\n            [campo]: valor\n        };\n        setTemas(nuevosTemasTemp);\n    };\n    const validarFormulario = ()=>{\n        if (!tituloTemario.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('El título del temario es obligatorio');\n            return false;\n        }\n        if (temas.some((tema)=>!tema.titulo.trim())) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Todos los temas deben tener un título');\n            return false;\n        }\n        return true;\n    };\n    const handleGuardar = async ()=>{\n        setIsLoading(true);\n        try {\n            let datosTemario;\n            if (tipoSeleccionado === 'predefinido' && temarioPredefinidoSeleccionado) {\n                // Usar datos del temario predefinido\n                datosTemario = (0,_services_temariosPredefinidosService__WEBPACK_IMPORTED_MODULE_3__.convertirTemarioParaCreacion)(temarioPredefinidoSeleccionado);\n            } else {\n                // Validaciones para temarios manuales\n                if (!validarFormulario() || !tipoSeleccionado) return;\n                datosTemario = {\n                    titulo: tituloTemario,\n                    descripcion: descripcionTemario,\n                    tipo: tipoSeleccionado,\n                    temas: temas.map((tema, index)=>({\n                            numero: tema.numero,\n                            titulo: tema.titulo,\n                            descripcion: tema.descripcion,\n                            orden: index + 1\n                        }))\n                };\n            }\n            // Crear el temario\n            const temarioId = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.crearTemario)(datosTemario.titulo, datosTemario.descripcion, datosTemario.tipo);\n            if (!temarioId) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Error al crear el temario');\n                return;\n            }\n            // Crear los temas\n            const temasCreados = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.crearTemas)(temarioId, datosTemario.temas);\n            if (!temasCreados) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Error al crear los temas');\n                return;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.success('¡Temario configurado exitosamente!');\n            onComplete();\n        } catch (error) {\n            console.error('Error al guardar temario:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Error al configurar el temario');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (paso === 'seleccion') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"\\xa1Bienvenido a OposiAI! \\uD83C\\uDF89\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 mb-2\",\n                                children: \"Para comenzar, necesitamos configurar tu temario de estudio.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Esto nos permitir\\xe1 crear una planificaci\\xf3n personalizada y hacer un seguimiento de tu progreso.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-green-500 cursor-pointer transition-all duration-200 hover:shadow-md\",\n                                onClick: ()=>handleTipoSeleccion('predefinido'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiDownload, {\n                                                className: \"w-8 h-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: \"Temarios Predefinidos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Selecciona un temario oficial ya configurado y listo para usar.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 border border-green-200 rounded-lg p-3 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-green-800 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiCheck, {\n                                                        className: \"w-4 h-4 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Basados en convocatorias oficiales\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 border border-green-200 rounded-lg p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-green-800 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiCheck, {\n                                                        className: \"w-4 h-4 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Configuraci\\xf3n instant\\xe1nea\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-blue-500 cursor-pointer transition-all duration-200 hover:shadow-md\",\n                                onClick: ()=>handleTipoSeleccion('completo'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiBook, {\n                                                className: \"w-8 h-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: \"Temario Personalizado\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Configura todos los temas de tu oposici\\xf3n de forma estructurada.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 border border-green-200 rounded-lg p-3 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-green-800 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiCheck, {\n                                                        className: \"w-4 h-4 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"La IA podr\\xe1 crear una planificaci\\xf3n completa y personalizada\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 border border-green-200 rounded-lg p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-green-800 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiCheck, {\n                                                        className: \"w-4 h-4 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Seguimiento detallado del progreso por temas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Podr\\xe1s modificar tu temario m\\xe1s adelante desde la configuraci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (paso === 'predefinidos') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemariosPredefinidosSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            onSeleccionar: handleTemarioPredefinidoSeleccionado,\n            onVolver: handleVolverDesdePredefinidos\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setPaso('seleccion'),\n                                className: \"text-blue-600 hover:text-blue-700 text-sm font-medium mb-4\",\n                                children: \"← Volver a la selecci\\xf3n\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: tipoSeleccionado === 'predefinido' ? 'Confirmar Temario Predefinido' : 'Configurar Temario Completo'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined),\n                    tipoSeleccionado === 'predefinido' && temarioPredefinidoSeleccionado ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-lg p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-900 mb-4\",\n                                children: \"Temario Seleccionado\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Nombre:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-700\",\n                                                children: temarioPredefinidoSeleccionado.nombre\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Cuerpo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-700\",\n                                                children: temarioPredefinidoSeleccionado.cuerpo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Descripci\\xf3n:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-700\",\n                                                children: temarioPredefinidoSeleccionado.descripcion\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Total de temas:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-700\",\n                                                children: [\n                                                    temarioPredefinidoSeleccionado.temas.length,\n                                                    \" temas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"titulo\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"T\\xedtulo del temario *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"titulo\",\n                                        value: tituloTemario,\n                                        onChange: (e)=>setTituloTemario(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        placeholder: \"Ej: Oposiciones Auxiliar Administrativo 2024\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"descripcion\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Descripci\\xf3n (opcional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"descripcion\",\n                                        value: descripcionTemario,\n                                        onChange: (e)=>setDescripcionTemario(e.target.value),\n                                        rows: 3,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        placeholder: \"Describe brevemente tu temario...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 13\n                    }, undefined),\n                    tipoSeleccionado !== 'predefinido' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Temas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: agregarTema,\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlus, {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"A\\xf1adir tema\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: temas.map((tema, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3 p-3 border border-gray-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs text-gray-500 mb-1\",\n                                                        children: \"Tema\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: tema.numero,\n                                                        onChange: (e)=>actualizarTema(index, 'numero', parseInt(e.target.value) || 1),\n                                                        className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                        min: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs text-gray-500 mb-1\",\n                                                        children: \"T\\xedtulo *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: tema.titulo,\n                                                        onChange: (e)=>actualizarTema(index, 'titulo', e.target.value),\n                                                        className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                        placeholder: \"T\\xedtulo del tema\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs text-gray-500 mb-1\",\n                                                        children: \"Descripci\\xf3n\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: tema.descripcion,\n                                                        onChange: (e)=>actualizarTema(index, 'descripcion', e.target.value),\n                                                        className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                        placeholder: \"Descripci\\xf3n opcional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            temas.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>eliminarTema(index),\n                                                className: \"text-red-600 hover:text-red-700 p-1\",\n                                                title: \"Eliminar tema\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiDownload_FiPlus_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setPaso('seleccion'),\n                                className: \"px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                disabled: isLoading,\n                                children: \"Cancelar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGuardar,\n                                disabled: isLoading,\n                                className: \"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Guardando...\"\n                                    ]\n                                }, void 0, true) : 'Guardar temario'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioSetup.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemarioSetup, \"rMWwpg+QwfsUTKEyY6KX5itJfek=\");\n_c = TemarioSetup;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemarioSetup);\nvar _c;\n$RefreshReg$(_c, \"TemarioSetup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/components/TemarioSetup.tsx\n"));

/***/ })

});