"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/flashcardsService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/flashcardsService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarFlashcard: () => (/* binding */ actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* binding */ actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* binding */ crearColeccionFlashcards),\n/* harmony export */   crearFlashcard: () => (/* binding */ crearFlashcard),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* binding */ eliminarColeccionFlashcards),\n/* harmony export */   eliminarFlashcard: () => (/* binding */ eliminarFlashcard),\n/* harmony export */   guardarFlashcards: () => (/* binding */ guardarFlashcards),\n/* harmony export */   guardarRevisionHistorial: () => (/* binding */ guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* binding */ obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* binding */ obtenerColeccionesFlashcards),\n/* harmony export */   obtenerFlashcardPorId: () => (/* binding */ obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* binding */ obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* binding */ obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* binding */ obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* binding */ obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* binding */ obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* binding */ obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* binding */ obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* binding */ obtenerProgresoFlashcard),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* binding */ registrarRespuestaFlashcard),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* binding */ reiniciarProgresoFlashcard)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea una nueva colección de flashcards\n */ async function crearColeccionFlashcards(titulo, descripcion) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').insert([\n            {\n                titulo,\n                descripcion,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear colección de flashcards:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al crear colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las colecciones de flashcards del usuario actual\n */ async function obtenerColeccionesFlashcards() {\n    try {\n        // Obtener el usuario actual\n        const { user, error: userError } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (userError) {\n            console.error('Error al obtener usuario:', userError);\n            return [];\n        }\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener colecciones de flashcards:', error);\n            return [];\n        }\n        if (!data || data.length === 0) {\n            return [];\n        }\n        // Obtener el conteo de flashcards y estadísticas básicas para cada colección\n        const coleccionesConConteo = await Promise.all(data.map(async (coleccion)=>{\n            try {\n                // Usar una consulta simple para contar flashcards\n                const { data: flashcardsData, error: flashcardsError } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccion.id);\n                if (flashcardsError) {\n                    console.error('Error al contar flashcards para colección', coleccion.id, ':', flashcardsError);\n                    return {\n                        ...coleccion,\n                        numero_flashcards: 0,\n                        pendientes_hoy: 0\n                    };\n                }\n                // Obtener flashcards pendientes para hoy\n                const { data: pendientesData, error: pendientesError } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select(\"\\n              id,\\n              progreso_flashcards!inner(\\n                proxima_revision,\\n                estado\\n              )\\n            \").eq('coleccion_id', coleccion.id).lte('progreso_flashcards.proxima_revision', new Date().toISOString());\n                const pendientesHoy = pendientesError ? 0 : (pendientesData === null || pendientesData === void 0 ? void 0 : pendientesData.length) || 0;\n                return {\n                    ...coleccion,\n                    numero_flashcards: (flashcardsData === null || flashcardsData === void 0 ? void 0 : flashcardsData.length) || 0,\n                    pendientes_hoy: pendientesHoy\n                };\n            } catch (error) {\n                console.error('Error al procesar colección', coleccion.id, ':', error);\n                return {\n                    ...coleccion,\n                    numero_flashcards: 0,\n                    pendientes_hoy: 0\n                };\n            }\n        }));\n        return coleccionesConConteo;\n    } catch (error) {\n        console.error('Error general al obtener colecciones de flashcards:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)\n */ async function obtenerColeccionFlashcardsPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener colección de flashcards:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva flashcard\n */ async function crearFlashcard(coleccionId, pregunta, respuesta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert([\n        {\n            coleccion_id: coleccionId,\n            pregunta,\n            respuesta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear flashcard:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las flashcards de una colección\n */ async function obtenerFlashcardsPorColeccionId(coleccionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener flashcards:', error);\n        return [];\n    }\n    return data || [];\n}\n// Alias para mantener compatibilidad con el código existente\nconst obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;\n/**\n * Guarda múltiples flashcards en la base de datos\n */ async function guardarFlashcards(flashcards) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert(flashcards).select();\n    if (error) {\n        console.error('Error al guardar flashcards:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((card)=>card.id)) || null;\n}\n/**\n * Obtiene una flashcard por su ID\n */ async function obtenerFlashcardPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener flashcard:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene el progreso de una flashcard\n */ async function obtenerProgresoFlashcard(flashcardId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').eq('flashcard_id', flashcardId).maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro\n    if (error) {\n        console.error('Error al obtener progreso de flashcard:', error);\n        return null;\n    }\n    return data || null;\n}\n/**\n * Obtiene todas las flashcards con su progreso para una colección\n */ async function obtenerFlashcardsParaEstudiar(coleccionId) {\n    // Obtener todas las flashcards de la colección\n    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);\n    // Obtener el progreso de todas las flashcards en una sola consulta\n    const { data: progresos, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((f)=>f.id));\n    if (error) {\n        console.error('Error al obtener progreso de flashcards:', error);\n        return [];\n    }\n    // Fecha actual para comparar\n    const ahora = new Date();\n    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n    // Combinar flashcards con su progreso\n    return flashcards.map((flashcard)=>{\n        const progreso = progresos === null || progresos === void 0 ? void 0 : progresos.find((p)=>p.flashcard_id === flashcard.id);\n        if (!progreso) {\n            // Si no hay progreso, es una tarjeta nueva que debe estudiarse\n            return {\n                ...flashcard,\n                debeEstudiar: true\n            };\n        }\n        // Determinar si la flashcard debe estudiarse hoy\n        const proximaRevision = new Date(progreso.proxima_revision);\n        const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n        const debeEstudiar = proximaRevisionSinHora <= hoy;\n        return {\n            ...flashcard,\n            debeEstudiar,\n            progreso: {\n                factor_facilidad: progreso.factor_facilidad,\n                intervalo: progreso.intervalo,\n                repeticiones: progreso.repeticiones,\n                estado: progreso.estado,\n                proxima_revision: progreso.proxima_revision\n            }\n        };\n    });\n}\n/**\n * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)\n */ async function obtenerFlashcardsMasDificiles(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        // Obtener todas las flashcards con progreso\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener historial de revisiones para calcular dificultad\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: historial, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, dificultad').in('flashcard_id', flashcardIds);\n        if (error) {\n            console.error('Error al obtener historial de revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Calcular estadísticas de dificultad por flashcard\n        const estadisticasDificultad = new Map();\n        historial === null || historial === void 0 ? void 0 : historial.forEach((revision)=>{\n            const stats = estadisticasDificultad.get(revision.flashcard_id) || {\n                dificil: 0,\n                total: 0\n            };\n            stats.total++;\n            if (revision.dificultad === 'dificil') {\n                stats.dificil++;\n            }\n            estadisticasDificultad.set(revision.flashcard_id, stats);\n        });\n        // Ordenar por dificultad (ratio de respuestas difíciles)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const stats = estadisticasDificultad.get(flashcard.id);\n            const ratioDificultad = stats ? stats.dificil / stats.total : 0;\n            return {\n                ...flashcard,\n                ratioDificultad\n            };\n        }).sort((a, b)=>b.ratioDificultad - a.ratioDificultad).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards más difíciles:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards aleatorias de una colección\n */ async function obtenerFlashcardsAleatorias(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Mezclar aleatoriamente y tomar el límite\n        const flashcardsMezcladas = [\n            ...flashcardsConProgreso\n        ].sort(()=>Math.random() - 0.5).slice(0, limite);\n        return flashcardsMezcladas;\n    } catch (error) {\n        console.error('Error al obtener flashcards aleatorias:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards que no se han estudiado recientemente\n */ async function obtenerFlashcardsNoRecientes(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener última revisión de cada flashcard\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: ultimasRevisiones, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, fecha').in('flashcard_id', flashcardIds).order('fecha', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener últimas revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Obtener la fecha más reciente por flashcard\n        const ultimaRevisionPorFlashcard = new Map();\n        ultimasRevisiones === null || ultimasRevisiones === void 0 ? void 0 : ultimasRevisiones.forEach((revision)=>{\n            if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {\n                ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha);\n            }\n        });\n        // Ordenar por fecha de última revisión (más antiguas primero)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const ultimaRevision = ultimaRevisionPorFlashcard.get(flashcard.id);\n            return {\n                ...flashcard,\n                ultimaRevision: ultimaRevision ? new Date(ultimaRevision) : new Date(0)\n            };\n        }).sort((a, b)=>a.ultimaRevision.getTime() - b.ultimaRevision.getTime()).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards no recientes:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards por estado específico\n */ async function obtenerFlashcardsPorEstado(coleccionId, estado) {\n    let limite = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Filtrar por estado y limitar\n        const flashcardsFiltradas = flashcardsConProgreso.filter((flashcard)=>{\n            if (!flashcard.progreso) {\n                return estado === 'nuevo';\n            }\n            return flashcard.progreso.estado === estado;\n        }).slice(0, limite);\n        return flashcardsFiltradas;\n    } catch (error) {\n        console.error('Error al obtener flashcards por estado:', error);\n        return [];\n    }\n}\n/**\n * Registra una respuesta a una flashcard y actualiza su progreso\n * Versión mejorada para evitar errores 409\n */ async function registrarRespuestaFlashcard(flashcardId, dificultad) {\n    try {\n        // Primero intentamos obtener el progreso existente\n        let factorFacilidad = 2.5;\n        let intervalo = 1;\n        let repeticiones = 0;\n        let estado = 'nuevo';\n        let progresoExiste = false;\n        // Intentar obtener progreso existente\n        const { data: progresoExistente, error: errorConsulta } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('factor_facilidad, intervalo, repeticiones, estado').eq('flashcard_id', flashcardId).single();\n        if (!errorConsulta && progresoExistente) {\n            factorFacilidad = progresoExistente.factor_facilidad || 2.5;\n            intervalo = progresoExistente.intervalo || 1;\n            repeticiones = progresoExistente.repeticiones || 0;\n            estado = progresoExistente.estado || 'nuevo';\n            progresoExiste = true;\n        }\n        // Aplicar el algoritmo SM-2 para calcular el nuevo progreso\n        let nuevoFactorFacilidad = factorFacilidad;\n        let nuevoIntervalo = intervalo;\n        let nuevasRepeticiones = repeticiones;\n        let nuevoEstado = estado;\n        // Ajustar el factor de facilidad según la dificultad reportada\n        if (dificultad === 'dificil') {\n            nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);\n            nuevasRepeticiones = 0;\n            nuevoIntervalo = 1;\n            nuevoEstado = 'aprendiendo';\n        } else {\n            nuevasRepeticiones++;\n            if (dificultad === 'normal') {\n                nuevoFactorFacilidad = factorFacilidad - 0.15;\n            } else if (dificultad === 'facil') {\n                nuevoFactorFacilidad = factorFacilidad + 0.1;\n            }\n            nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));\n            // Calcular el nuevo intervalo\n            if (nuevasRepeticiones === 1) {\n                nuevoIntervalo = 1;\n                nuevoEstado = 'aprendiendo';\n            } else if (nuevasRepeticiones === 2) {\n                nuevoIntervalo = 6;\n                nuevoEstado = 'repasando';\n            } else {\n                nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);\n                nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';\n            }\n        }\n        // Calcular la próxima fecha de revisión\n        const ahora = new Date();\n        const proximaRevision = new Date(ahora);\n        proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);\n        // Guardar el nuevo progreso usando insert o update según corresponda\n        let errorProgreso = null;\n        if (progresoExiste) {\n            // Actualizar progreso existente\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            }).eq('flashcard_id', flashcardId);\n            errorProgreso = error;\n        } else {\n            // Crear nuevo progreso\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').insert({\n                flashcard_id: flashcardId,\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            });\n            errorProgreso = error;\n        }\n        if (errorProgreso) {\n            console.error('Error al guardar progreso:', errorProgreso);\n            return false;\n        }\n        // Guardar en el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert({\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: nuevoFactorFacilidad,\n            intervalo: nuevoIntervalo,\n            repeticiones: nuevasRepeticiones,\n            fecha: ahora.toISOString()\n        });\n        if (errorHistorial) {\n        // No retornamos false aquí porque el progreso ya se guardó correctamente\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n// Alias para mantener compatibilidad con el código existente\nconst actualizarProgresoFlashcard = registrarRespuestaFlashcard;\n/**\n * Guarda una revisión en el historial\n */ async function guardarRevisionHistorial(flashcardId, dificultad, factorFacilidad, intervalo, repeticiones) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert([\n        {\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: factorFacilidad,\n            intervalo,\n            repeticiones\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al guardar revisión en historial:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Reinicia el progreso de una flashcard\n */ async function reiniciarProgresoFlashcard(flashcardId) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n        factor_facilidad: 2.5,\n        intervalo: 0,\n        repeticiones: 0,\n        estado: 'nuevo',\n        ultima_revision: new Date().toISOString(),\n        proxima_revision: new Date().toISOString()\n    }).eq('flashcard_id', flashcardId);\n    if (error) {\n        console.error('Error al reiniciar progreso de flashcard:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Actualiza una flashcard existente\n */ async function actualizarFlashcard(flashcardId, pregunta, respuesta) {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').update({\n            pregunta,\n            respuesta,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', flashcardId);\n        if (error) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una flashcard y todo su progreso asociado\n */ async function eliminarFlashcard(flashcardId) {\n    try {\n        // Primero eliminar el progreso asociado\n        const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().eq('flashcard_id', flashcardId);\n        if (errorProgreso) {\n            return false;\n        }\n        // Eliminar el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().eq('flashcard_id', flashcardId);\n        if (errorHistorial) {\n            return false;\n        }\n        // Finalmente eliminar la flashcard\n        const { error: errorFlashcard, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete({\n            count: 'exact'\n        }).eq('id', flashcardId);\n        if (errorFlashcard) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una colección completa de flashcards y todo su contenido asociado\n */ async function eliminarColeccionFlashcards(coleccionId) {\n    try {\n        // Obtener el usuario actual para verificar permisos\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return false;\n        }\n        // Primero obtener todas las flashcards de la colección\n        const { data: flashcards, error: errorFlashcards } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccionId);\n        if (errorFlashcards) {\n            return false;\n        }\n        const flashcardIds = (flashcards === null || flashcards === void 0 ? void 0 : flashcards.map((fc)=>fc.id)) || [];\n        // Eliminar progreso de todas las flashcards\n        if (flashcardIds.length > 0) {\n            const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().in('flashcard_id', flashcardIds);\n            if (errorProgreso) {\n                return false;\n            }\n            // Eliminar historial de todas las flashcards\n            const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().in('flashcard_id', flashcardIds);\n            if (errorHistorial) {\n                return false;\n            }\n            // Eliminar todas las flashcards de la colección\n            const { error: errorFlashcardsDelete } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete().eq('coleccion_id', coleccionId);\n            if (errorFlashcardsDelete) {\n                return false;\n            }\n        }\n        // Finalmente eliminar la colección\n        const { error: errorColeccion, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').delete({\n            count: 'exact'\n        }).eq('id', coleccionId).eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual\n        if (errorColeccion) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\n"));

/***/ })

});