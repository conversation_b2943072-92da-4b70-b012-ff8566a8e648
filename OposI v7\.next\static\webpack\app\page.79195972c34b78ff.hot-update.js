"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx":
/*!************************************************************!*\
  !*** ./src/features/temario/components/TemarioManager.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiEdit,FiSettings,FiTrash2,FiTrendingUp,FiZap!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(app-pages-browser)/./src/features/auth/services/authService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _features_planificacion_components_PlanificacionAsistente__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/planificacion/components/PlanificacionAsistente */ \"(app-pages-browser)/./src/features/planificacion/components/PlanificacionAsistente.tsx\");\n/* harmony import */ var _TemarioEditModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TemarioEditModal */ \"(app-pages-browser)/./src/features/temario/components/TemarioEditModal.tsx\");\n/* harmony import */ var _TemaEditModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./TemaEditModal */ \"(app-pages-browser)/./src/features/temario/components/TemaEditModal.tsx\");\n/* harmony import */ var _TemaActions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./TemaActions */ \"(app-pages-browser)/./src/features/temario/components/TemaActions.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst TemarioManager = ()=>{\n    _s();\n    const [temario, setTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temas, setTemas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [actualizandoTema, setActualizandoTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarAsistentePlanificacion, setMostrarAsistentePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarModalEdicion, setMostrarModalEdicion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarModalEdicionTema, setMostrarModalEdicionTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [temaSeleccionado, setTemaSeleccionado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mostrarConfirmacionEliminar, setMostrarConfirmacionEliminar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eliminandoTemario, setEliminandoTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TemarioManager.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"TemarioManager.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            const temarioData = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemarioUsuario)();\n            if (temarioData) {\n                setTemario(temarioData);\n                const [temasData, estadisticasData, planificacionConfigurada] = await Promise.all([\n                    (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemas)(temarioData.id),\n                    (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temarioData.id),\n                    (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.tienePlanificacionConfigurada)(temarioData.id)\n                ]);\n                setTemas(temasData);\n                setEstadisticas(estadisticasData);\n                setTienePlanificacion(planificacionConfigurada);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del temario:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al cargar el temario');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleToggleCompletado = async (temaId, completado)=>{\n        setActualizandoTema(temaId);\n        try {\n            const exito = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.actualizarEstadoTema)(temaId, !completado);\n            if (exito) {\n                // Actualizar el estado local\n                setTemas(temas.map((tema)=>tema.id === temaId ? {\n                        ...tema,\n                        completado: !completado,\n                        fecha_completado: !completado ? new Date().toISOString() : undefined\n                    } : tema));\n                // Recalcular estadísticas\n                if (temario) {\n                    const nuevasEstadisticas = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temario.id);\n                    setEstadisticas(nuevasEstadisticas);\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(!completado ? 'Tema marcado como completado' : 'Tema marcado como pendiente');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al actualizar el estado del tema');\n            }\n        } catch (error) {\n            console.error('Error al actualizar tema:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al actualizar el tema');\n        } finally{\n            setActualizandoTema(null);\n        }\n    };\n    const formatearFecha = (fecha)=>{\n        return new Date(fecha).toLocaleDateString('es-ES', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric'\n        });\n    };\n    const handleIniciarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(true);\n    };\n    const handleModificarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(true);\n    };\n    const handleCompletarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(false);\n        setTienePlanificacion(true);\n        // Recargar datos para reflejar los cambios\n        cargarDatos();\n    };\n    const handleCancelarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(false);\n    };\n    const handleEditarTemario = ()=>{\n        setMostrarModalEdicion(true);\n    };\n    const handleGuardarTemario = (temarioActualizado)=>{\n        setTemario(temarioActualizado);\n        setMostrarModalEdicion(false);\n    };\n    const handleCancelarEdicion = ()=>{\n        setMostrarModalEdicion(false);\n    };\n    const handleEditarTema = (tema)=>{\n        setTemaSeleccionado(tema);\n        setMostrarModalEdicionTema(true);\n    };\n    const handleGuardarTema = (temaActualizado)=>{\n        setTemas(temas.map((tema)=>tema.id === temaActualizado.id ? temaActualizado : tema));\n        setMostrarModalEdicionTema(false);\n        setTemaSeleccionado(null);\n    };\n    const handleCancelarEdicionTema = ()=>{\n        setMostrarModalEdicionTema(false);\n        setTemaSeleccionado(null);\n    };\n    const handleEliminarTema = async (temaId)=>{\n        // Eliminar tema del estado local\n        setTemas(temas.filter((tema)=>tema.id !== temaId));\n        // Recalcular estadísticas\n        if (temario) {\n            const nuevasEstadisticas = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temario.id);\n            setEstadisticas(nuevasEstadisticas);\n        }\n    };\n    const handleEliminarTemario = ()=>{\n        setMostrarConfirmacionEliminar(true);\n    };\n    const handleConfirmarEliminarTemario = async ()=>{\n        if (!temario) return;\n        setEliminandoTemario(true);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.loading('Eliminando temario y desactivando plan de estudios...');\n            // 1. Obtener y eliminar planificación si existe\n            const { user } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_5__.obtenerUsuarioActual)();\n            if (user) {\n                await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.supabase.from('planificacion_usuario').delete().eq('user_id', user.id).eq('temario_id', temario.id);\n            }\n            // 2. Desactivar planes de estudios si existen (en lugar de eliminarlos)\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.supabase.from('planes_estudios').update({\n                activo: false\n            }).eq('temario_id', temario.id);\n            // 3. Eliminar el temario (esto eliminará automáticamente los temas por CASCADE)\n            const success = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.eliminarTemario)(temario.id);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success('Temario eliminado y plan de estudios desactivado exitosamente', {\n                    id: loadingToastId\n                });\n                // Limpiar estado y recargar\n                setTemario(null);\n                setTemas([]);\n                setEstadisticas(null);\n                setTienePlanificacion(false);\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al eliminar el temario', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar temario:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al eliminar el temario', {\n                id: loadingToastId\n            });\n        } finally{\n            setEliminandoTemario(false);\n            setMostrarConfirmacionEliminar(false);\n        }\n    };\n    const handleCancelarEliminarTemario = ()=>{\n        setMostrarConfirmacionEliminar(false);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!temario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiBook, {\n                    className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No hay temario configurado\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"Configura tu temario desde el dashboard para comenzar.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar asistente de planificación si está activo\n    if (mostrarAsistentePlanificacion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanificacionAsistente__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            temario: temario,\n            onComplete: handleCompletarPlanificacion,\n            onCancel: handleCancelarPlanificacion,\n            isEditing: tienePlanificacion\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: temario.titulo\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    temario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: temario.descripcion\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(temario.tipo === 'completo' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'),\n                                                children: temario.tipo === 'completo' ? 'Temario Completo' : 'Temas Sueltos'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"Creado el \",\n                                                    formatearFecha(temario.creado_en)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleEditarTemario,\n                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors\",\n                                        title: \"Editar temario\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiEdit, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleEliminarTemario,\n                                        className: \"p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors\",\n                                        title: \"Eliminar temario\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiTrash2, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiBook, {\n                                            className: \"w-5 h-5 text-blue-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Total Temas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: estadisticas.totalTemas\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCheck, {\n                                            className: \"w-5 h-5 text-green-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"Completados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: estadisticas.temasCompletados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiTrendingUp, {\n                                            className: \"w-5 h-5 text-purple-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-purple-800\",\n                                                    children: \"Progreso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: [\n                                                        estadisticas.porcentajeCompletado.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, undefined),\n                    estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Progreso del temario\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            estadisticas.porcentajeCompletado.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: \"\".concat(estadisticas.porcentajeCompletado, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, undefined),\n            temario.tipo === 'completo' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-xl p-6 \".concat(tienePlanificacion ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                tienePlanificacion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCheck, {\n                                    className: \"w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiZap, {\n                                    className: \"w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2 \".concat(tienePlanificacion ? 'text-green-900' : 'text-blue-900'),\n                                            children: tienePlanificacion ? 'Planificación Configurada' : 'Planificación Inteligente con IA'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mb-3 \".concat(tienePlanificacion ? 'text-green-800' : 'text-blue-800'),\n                                            children: tienePlanificacion ? 'Ya tienes configurada tu planificación de estudio personalizada. Pronto podrás ver tu calendario y seguimiento.' : 'Configura tu planificación personalizada con nuestro asistente inteligente:'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-blue-800 text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Planificaci\\xf3n autom\\xe1tica de estudio con IA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Seguimiento de progreso personalizado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Recomendaciones de orden de estudio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Estimaci\\xf3n de tiempos por tema\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: !tienePlanificacion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleIniciarPlanificacion,\n                                className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiZap, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Configurar Planificaci\\xf3n\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModificarPlanificacion,\n                                className: \"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiSettings, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Modificar Planificaci\\xf3n\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 350,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Temas del Temario\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Marca los temas como completados seg\\xfan vayas estudi\\xe1ndolos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: temas.map((tema)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium\",\n                                                        children: tema.numero\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium \".concat(tema.completado ? 'text-gray-500 line-through' : 'text-gray-900'),\n                                                            children: tema.titulo\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        tema.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: tema.descripcion\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        tema.fecha_completado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2 text-sm text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCheck, {\n                                                                    className: \"w-4 h-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"Completado el \",\n                                                                formatearFecha(tema.fecha_completado)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemaActions__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                tema: tema,\n                                                onEdit: handleEditarTema,\n                                                onDelete: handleEliminarTema,\n                                                onToggleCompletado: handleToggleCompletado,\n                                                isUpdating: actualizandoTema === tema.id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tema.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined),\n            temario && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemarioEditModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: mostrarModalEdicion,\n                onClose: handleCancelarEdicion,\n                temario: temario,\n                onSave: handleGuardarTemario\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 462,\n                columnNumber: 9\n            }, undefined),\n            temaSeleccionado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemaEditModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: mostrarModalEdicionTema,\n                onClose: handleCancelarEdicionTema,\n                tema: temaSeleccionado,\n                onSave: handleGuardarTema\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 472,\n                columnNumber: 9\n            }, undefined),\n            mostrarConfirmacionEliminar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiTrash2, {\n                                        className: \"w-6 h-6 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Eliminar Temario\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"\\xbfEst\\xe1s seguro de que quieres eliminar este temario? Esta acci\\xf3n:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-red-600 space-y-1 ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                '• Eliminar\\xe1 permanentemente el temario \"',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: temario === null || temario === void 0 ? void 0 : temario.titulo\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 61\n                                                }, undefined),\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Eliminar\\xe1 todos los temas asociados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Eliminar\\xe1 la planificaci\\xf3n de estudios configurada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Desactivar\\xe1 los planes de estudios generados (se conservan en el historial)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-3 font-medium\",\n                                    children: \"Esta acci\\xf3n no se puede deshacer.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancelarEliminarTemario,\n                                    disabled: eliminandoTemario,\n                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleConfirmarEliminarTemario,\n                                    disabled: eliminandoTemario,\n                                    className: \"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center\",\n                                    children: eliminandoTemario ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Eliminando...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrash2_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiTrash2, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Eliminar Temario\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 482,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemarioManager, \"IMwz7Otk8TD65pRu7+3XZOgxmnE=\");\n_c = TemarioManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemarioManager);\nvar _c;\n$RefreshReg$(_c, \"TemarioManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\n"));

/***/ })

});