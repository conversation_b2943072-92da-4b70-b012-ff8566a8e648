"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/flashcards/components/FlashcardGeneralStatistics.tsx":
/*!***************************************************************************!*\
  !*** ./src/features/flashcards/components/FlashcardGeneralStatistics.tsx ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiAward_FiBookOpen_FiClock_FiTarget_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiAward,FiBookOpen,FiClock,FiTarget,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst FlashcardGeneralStatistics = (param)=>{\n    let { onClose } = param;\n    _s();\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardGeneralStatistics.useEffect\": ()=>{\n            cargarEstadisticasGenerales();\n        }\n    }[\"FlashcardGeneralStatistics.useEffect\"], []);\n    const cargarEstadisticasGenerales = async ()=>{\n        try {\n            setIsLoading(true);\n            // Obtener todas las colecciones\n            const colecciones = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n            if (colecciones.length === 0) {\n                setEstadisticas({\n                    totalColecciones: 0,\n                    totalFlashcards: 0,\n                    totalNuevas: 0,\n                    totalAprendiendo: 0,\n                    totalRepasando: 0,\n                    totalAprendidas: 0,\n                    totalParaHoy: 0,\n                    coleccionesConMasActividad: []\n                });\n                return;\n            }\n            // Obtener estadísticas de cada colección\n            const estadisticasColecciones = await Promise.all(colecciones.map(async (coleccion)=>{\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n                return {\n                    coleccion,\n                    estadisticas: stats\n                };\n            }));\n            // Calcular estadísticas generales\n            const estadisticasGenerales = {\n                totalColecciones: colecciones.length,\n                totalFlashcards: estadisticasColecciones.reduce((sum, item)=>sum + item.estadisticas.total, 0),\n                totalNuevas: estadisticasColecciones.reduce((sum, item)=>sum + item.estadisticas.nuevas, 0),\n                totalAprendiendo: estadisticasColecciones.reduce((sum, item)=>sum + item.estadisticas.aprendiendo, 0),\n                totalRepasando: estadisticasColecciones.reduce((sum, item)=>sum + item.estadisticas.repasando, 0),\n                totalAprendidas: estadisticasColecciones.reduce((sum, item)=>sum + item.estadisticas.aprendidas, 0),\n                totalParaHoy: estadisticasColecciones.reduce((sum, item)=>sum + item.estadisticas.paraHoy, 0),\n                coleccionesConMasActividad: estadisticasColecciones.map((item)=>({\n                        id: item.coleccion.id,\n                        titulo: item.coleccion.titulo,\n                        totalRevisiones: item.estadisticas.total,\n                        paraHoy: item.estadisticas.paraHoy\n                    })).sort((a, b)=>b.paraHoy - a.paraHoy).slice(0, 5)\n            };\n            setEstadisticas(estadisticasGenerales);\n        } catch (error) {\n            console.error('Error al cargar estadísticas generales:', error);\n            setError('No se pudieron cargar las estadísticas generales');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-40\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold\",\n                                children: \"Error\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-gray-500 hover:text-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAward_FiBookOpen_FiClock_FiTarget_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiX, {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!estadisticas) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"Estad\\xedsticas Generales de Flashcards\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAward_FiBookOpen_FiClock_FiTarget_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiX, {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAward_FiBookOpen_FiClock_FiTarget_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiBookOpen, {\n                                            className: \"text-blue-600 mr-2 text-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold\",\n                                            children: \"Total Colecciones\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-blue-700\",\n                                    children: estadisticas.totalColecciones\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAward_FiBookOpen_FiClock_FiTarget_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiTarget, {\n                                            className: \"text-green-600 mr-2 text-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold\",\n                                            children: \"Total Flashcards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-green-700\",\n                                    children: estadisticas.totalFlashcards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-50 border border-orange-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAward_FiBookOpen_FiClock_FiTarget_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiClock, {\n                                            className: \"text-orange-600 mr-2 text-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold\",\n                                            children: \"Para Estudiar Hoy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-orange-700\",\n                                    children: estadisticas.totalParaHoy\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAward_FiBookOpen_FiClock_FiTarget_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiAward, {\n                                            className: \"text-purple-600 mr-2 text-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold\",\n                                            children: \"Aprendidas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-purple-700\",\n                                    children: estadisticas.totalAprendidas\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-lg font-semibold mb-3\",\n                            children: \"Distribuci\\xf3n por Estado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: estadisticas.totalNuevas\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Nuevas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-yellow-600\",\n                                                children: estadisticas.totalAprendiendo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Aprendiendo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-orange-600\",\n                                                children: estadisticas.totalRepasando\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Repasando\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: estadisticas.totalAprendidas\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Aprendidas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined),\n                estadisticas.coleccionesConMasActividad.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-lg font-semibold mb-3\",\n                            children: \"Colecciones con M\\xe1s Actividad\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: estadisticas.coleccionesConMasActividad.map((coleccion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-blue-600 mr-3\",\n                                                        children: [\n                                                            \"#\",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: coleccion.titulo\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    coleccion.totalRevisiones,\n                                                                    \" flashcards total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-orange-600\",\n                                                        children: coleccion.paraHoy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"para hoy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, coleccion.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-lg font-semibold mb-3\",\n                            children: \"Resumen de Progreso\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: [\n                                                    estadisticas.totalFlashcards > 0 ? Math.round(estadisticas.totalAprendidas / estadisticas.totalFlashcards * 100) : 0,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Progreso General\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: [\n                                                    estadisticas.totalFlashcards > 0 ? Math.round((estadisticas.totalAprendidas + estadisticas.totalRepasando) / estadisticas.totalFlashcards * 100) : 0,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"En Proceso\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-orange-600\",\n                                                children: estadisticas.totalParaHoy\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Pendientes Hoy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                        children: \"Cerrar\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardGeneralStatistics.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardGeneralStatistics, \"U3dWQsCSwqm7oQrwOSJCSsXAMmY=\");\n_c = FlashcardGeneralStatistics;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardGeneralStatistics);\nvar _c;\n$RefreshReg$(_c, \"FlashcardGeneralStatistics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/flashcards/components/FlashcardGeneralStatistics.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx":
/*!****************************************************************!*\
  !*** ./src/features/flashcards/components/FlashcardViewer.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashcardViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardCollectionView */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardCollectionView.tsx\");\n/* harmony import */ var _FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyOptions */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardStudyOptions.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardStudyMode.tsx\");\n/* harmony import */ var _FlashcardEditModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FlashcardEditModal */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardEditModal.tsx\");\n/* harmony import */ var _FlashcardGeneralStatistics__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./FlashcardGeneralStatistics */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGeneralStatistics.tsx\");\n/* harmony import */ var _dashboard_components_StudyStatistics__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../dashboard/components/StudyStatistics */ \"(app-pages-browser)/./src/features/dashboard/components/StudyStatistics.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction FlashcardViewer() {\n    _s();\n    // Estados principales\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingColecciones, setIsLoadingColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modales y modos\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarOpcionesEstudio, setMostrarOpcionesEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticas, setMostrarEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticasGenerales, setMostrarEstadisticasGenerales] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEditarFlashcard, setMostrarEditarFlashcard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [flashcardParaEditar, setFlashcardParaEditar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingFlashcardId, setDeletingFlashcardId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoadingColecciones(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoadingColecciones(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const iniciarModoEstudio = async function() {\n        let tipoEstudio = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'programadas';\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                let flashcardsParaEstudiar = [];\n                // Obtener flashcards según el tipo de estudio seleccionado\n                switch(tipoEstudio){\n                    case 'programadas':\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                        break;\n                    case 'dificiles':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsMasDificiles)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'aleatorias':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsAleatorias)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'no-recientes':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsNoRecientes)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'nuevas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'nuevo', 20);\n                        break;\n                    case 'aprendiendo':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendiendo', 20);\n                        break;\n                    case 'repasando':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'repasando', 20);\n                        break;\n                    case 'aprendidas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendido', 20);\n                        break;\n                    default:\n                        const defaultData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = defaultData.filter((flashcard)=>flashcard.debeEstudiar);\n                }\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Si no hay flashcards para el tipo seleccionado, mostrar mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (tipoEstudio === 'programadas') {\n                        alert('No hay flashcards programadas para estudiar hoy. Puedes usar \"Opciones de estudio\" para elegir otro tipo de repaso.');\n                        return;\n                    } else {\n                        alert(\"No hay flashcards disponibles para el tipo de estudio seleccionado.\");\n                        return;\n                    }\n                }\n                // Usar las flashcards obtenidas\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarOpcionesEstudio(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo estudio:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const salirModoEstudio = async ()=>{\n        setModoEstudio(false);\n        // Recargar las flashcards y estadísticas\n        if (coleccionSeleccionada) {\n            try {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const ordenadas = [\n                    ...data\n                ].sort((a, b)=>{\n                    if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                    if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                    return 0;\n                });\n                setFlashcards(ordenadas);\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n            } catch (error) {\n                console.error('Error al recargar datos:', error);\n            }\n        }\n    };\n    const handleRespuesta = async (dificultad)=>{\n        if (!flashcards[activeIndex]) return;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcards[activeIndex].id, dificultad);\n            if (!exito) {\n                throw new Error('Error al registrar la respuesta');\n            }\n            // Si es la última tarjeta, terminar la sesión\n            if (activeIndex >= flashcards.length - 1) {\n                alert('¡Has completado la sesión de estudio!');\n                await salirModoEstudio();\n            } else {\n                // Avanzar a la siguiente tarjeta\n                setActiveIndex(activeIndex + 1);\n            }\n        } catch (error) {\n            console.error('Error al actualizar progreso:', error);\n            setError('Error al guardar tu respuesta. Por favor, inténtalo de nuevo.');\n        } finally{\n            setRespondiendo(false);\n        }\n    };\n    const handleNavigate = (direction)=>{\n        if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n        } else if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n        }\n    };\n    const handleEliminarColeccion = async (coleccionId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta colección? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingId(coleccionId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (exito) {\n                // Recargar las colecciones\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                setColecciones(data);\n                // Si la colección eliminada era la seleccionada, deseleccionarla\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                setError('No se pudo eliminar la colección');\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            setError('Error al eliminar la colección');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardParaEditar(flashcard);\n        setMostrarEditarFlashcard(true);\n    };\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta flashcard? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingFlashcardId(flashcardId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (exito) {\n                // Recargar las flashcards de la colección actual\n                if (coleccionSeleccionada) {\n                    const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                    // Actualizar estadísticas\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                setError('No se pudo eliminar la flashcard');\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            setError('Error al eliminar la flashcard');\n        } finally{\n            setDeletingFlashcardId(null);\n        }\n    };\n    const handleGuardarFlashcard = async (flashcardActualizada)=>{\n        // Actualizar la flashcard en el estado local\n        setFlashcards((prevFlashcards)=>prevFlashcards.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: salirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Mis Flashcards\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMostrarEstadisticasGenerales(true),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart2, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Estad\\xedsticas Generales\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this),\n                    !coleccionSeleccionada ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: isLoadingColecciones,\n                        deletingId: deletingId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setColeccionSeleccionada(null),\n                                className: \"mb-4 text-blue-600 hover:text-blue-800 flex items-center\",\n                                children: \"← Volver a mis colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                coleccion: coleccionSeleccionada,\n                                flashcards: flashcards,\n                                estadisticas: estadisticas,\n                                isLoading: isLoading,\n                                onStartStudy: ()=>iniciarModoEstudio('programadas'),\n                                onShowStudyOptions: ()=>setMostrarOpcionesEstudio(true),\n                                onShowStatistics: ()=>setMostrarEstadisticas(true),\n                                onEditFlashcard: handleEditarFlashcard,\n                                onDeleteFlashcard: handleEliminarFlashcard,\n                                deletingFlashcardId: deletingFlashcardId\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: mostrarOpcionesEstudio,\n                onClose: ()=>setMostrarOpcionesEstudio(false),\n                onSelectStudyType: iniciarModoEstudio,\n                estadisticas: estadisticas,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            mostrarEstadisticas && coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dashboard_components_StudyStatistics__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                coleccionId: coleccionSeleccionada.id,\n                onClose: ()=>setMostrarEstadisticas(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            mostrarEstadisticasGenerales && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardGeneralStatistics__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                colecciones: colecciones,\n                onClose: ()=>setMostrarEstadisticasGenerales(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this),\n            flashcardParaEditar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardEditModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                flashcard: flashcardParaEditar,\n                isOpen: mostrarEditarFlashcard,\n                onClose: ()=>{\n                    setMostrarEditarFlashcard(false);\n                    setFlashcardParaEditar(null);\n                },\n                onSave: handleGuardarFlashcard\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n        lineNumber: 305,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardViewer, \"omgmcgMuCAZRp5VCnCl8CFZhiiM=\");\n_c = FlashcardViewer;\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\n"));

/***/ })

});