"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx":
/*!****************************************************************!*\
  !*** ./src/features/flashcards/components/FlashcardViewer.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashcardViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardCollectionView */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardCollectionView.tsx\");\n/* harmony import */ var _FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyOptions */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardStudyOptions.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardStudyMode.tsx\");\n/* harmony import */ var _FlashcardEditModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FlashcardEditModal */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardEditModal.tsx\");\n/* harmony import */ var _dashboard_components_StudyStatistics__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../dashboard/components/StudyStatistics */ \"(app-pages-browser)/./src/features/dashboard/components/StudyStatistics.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction FlashcardViewer() {\n    _s();\n    // Estados principales\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingColecciones, setIsLoadingColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modales y modos\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarOpcionesEstudio, setMostrarOpcionesEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticas, setMostrarEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticasGenerales, setMostrarEstadisticasGenerales] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEditarFlashcard, setMostrarEditarFlashcard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [flashcardParaEditar, setFlashcardParaEditar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingFlashcardId, setDeletingFlashcardId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoadingColecciones(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoadingColecciones(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const iniciarModoEstudio = async function() {\n        let tipoEstudio = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'programadas';\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                let flashcardsParaEstudiar = [];\n                // Obtener flashcards según el tipo de estudio seleccionado\n                switch(tipoEstudio){\n                    case 'programadas':\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                        break;\n                    case 'dificiles':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsMasDificiles)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'aleatorias':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsAleatorias)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'no-recientes':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsNoRecientes)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'nuevas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'nuevo', 20);\n                        break;\n                    case 'aprendiendo':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendiendo', 20);\n                        break;\n                    case 'repasando':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'repasando', 20);\n                        break;\n                    case 'aprendidas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendido', 20);\n                        break;\n                    default:\n                        const defaultData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = defaultData.filter((flashcard)=>flashcard.debeEstudiar);\n                }\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Si no hay flashcards para el tipo seleccionado, mostrar mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (tipoEstudio === 'programadas') {\n                        alert('No hay flashcards programadas para estudiar hoy. Puedes usar \"Opciones de estudio\" para elegir otro tipo de repaso.');\n                        return;\n                    } else {\n                        alert(\"No hay flashcards disponibles para el tipo de estudio seleccionado.\");\n                        return;\n                    }\n                }\n                // Usar las flashcards obtenidas\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarOpcionesEstudio(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo estudio:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const salirModoEstudio = async ()=>{\n        setModoEstudio(false);\n        // Recargar las flashcards y estadísticas\n        if (coleccionSeleccionada) {\n            try {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const ordenadas = [\n                    ...data\n                ].sort((a, b)=>{\n                    if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                    if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                    return 0;\n                });\n                setFlashcards(ordenadas);\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n            } catch (error) {\n                console.error('Error al recargar datos:', error);\n            }\n        }\n    };\n    const handleRespuesta = async (dificultad)=>{\n        if (!flashcards[activeIndex]) return;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcards[activeIndex].id, dificultad);\n            if (!exito) {\n                throw new Error('Error al registrar la respuesta');\n            }\n            // Si es la última tarjeta, terminar la sesión\n            if (activeIndex >= flashcards.length - 1) {\n                alert('¡Has completado la sesión de estudio!');\n                await salirModoEstudio();\n            } else {\n                // Avanzar a la siguiente tarjeta\n                setActiveIndex(activeIndex + 1);\n            }\n        } catch (error) {\n            console.error('Error al actualizar progreso:', error);\n            setError('Error al guardar tu respuesta. Por favor, inténtalo de nuevo.');\n        } finally{\n            setRespondiendo(false);\n        }\n    };\n    const handleNavigate = (direction)=>{\n        if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n        } else if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n        }\n    };\n    const handleEliminarColeccion = async (coleccionId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta colección? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingId(coleccionId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (exito) {\n                // Recargar las colecciones\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                setColecciones(data);\n                // Si la colección eliminada era la seleccionada, deseleccionarla\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                setError('No se pudo eliminar la colección');\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            setError('Error al eliminar la colección');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardParaEditar(flashcard);\n        setMostrarEditarFlashcard(true);\n    };\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta flashcard? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingFlashcardId(flashcardId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (exito) {\n                // Recargar las flashcards de la colección actual\n                if (coleccionSeleccionada) {\n                    const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                    // Actualizar estadísticas\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                setError('No se pudo eliminar la flashcard');\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            setError('Error al eliminar la flashcard');\n        } finally{\n            setDeletingFlashcardId(null);\n        }\n    };\n    const handleGuardarFlashcard = async (flashcardActualizada)=>{\n        // Actualizar la flashcard en el estado local\n        setFlashcards((prevFlashcards)=>prevFlashcards.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: salirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Mis Flashcards\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMostrarEstadisticasGenerales(true),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiBarChart2, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Estad\\xedsticas Generales\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this),\n                    !coleccionSeleccionada ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: isLoadingColecciones,\n                        deletingId: deletingId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setColeccionSeleccionada(null),\n                                className: \"mb-4 text-blue-600 hover:text-blue-800 flex items-center\",\n                                children: \"← Volver a mis colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                coleccion: coleccionSeleccionada,\n                                flashcards: flashcards,\n                                estadisticas: estadisticas,\n                                isLoading: isLoading,\n                                onStartStudy: ()=>iniciarModoEstudio('programadas'),\n                                onShowStudyOptions: ()=>setMostrarOpcionesEstudio(true),\n                                onShowStatistics: ()=>setMostrarEstadisticas(true),\n                                onEditFlashcard: handleEditarFlashcard,\n                                onDeleteFlashcard: handleEliminarFlashcard,\n                                deletingFlashcardId: deletingFlashcardId\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: mostrarOpcionesEstudio,\n                onClose: ()=>setMostrarOpcionesEstudio(false),\n                onSelectStudyType: iniciarModoEstudio,\n                estadisticas: estadisticas,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            mostrarEstadisticas && coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dashboard_components_StudyStatistics__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                coleccionId: coleccionSeleccionada.id,\n                onClose: ()=>setMostrarEstadisticas(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            flashcardParaEditar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardEditModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                flashcard: flashcardParaEditar,\n                isOpen: mostrarEditarFlashcard,\n                onClose: ()=>{\n                    setMostrarEditarFlashcard(false);\n                    setFlashcardParaEditar(null);\n                },\n                onSave: handleGuardarFlashcard\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\flashcards\\\\components\\\\FlashcardViewer.tsx\",\n        lineNumber: 305,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardViewer, \"omgmcgMuCAZRp5VCnCl8CFZhiiM=\");\n_c = FlashcardViewer;\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\n"));

/***/ })

});