"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx":
/*!****************************************************************!*\
  !*** ./src/features/conversations/components/QuestionForm.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _ConversationSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ConversationSidebar */ \"(app-pages-browser)/./src/features/conversations/components/ConversationSidebar.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../lib/formSchemas */ \"(app-pages-browser)/./src/lib/formSchemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuestionForm(param) {\n    let { documentosSeleccionados } = param;\n    _s();\n    const [mensajes, setMensajes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [conversacionActualId, setConversacionActualId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [guardandoConversacion, setGuardandoConversacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { register, handleSubmit: handleSubmitForm, formState: { errors }, reset, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(_lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__.preguntaFormSchema),\n        defaultValues: {\n            pregunta: '',\n            documentos: documentosSeleccionados\n        }\n    });\n    // Sincronizar documentos seleccionados con el formulario, asegurando tipos correctos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const documentosValidados = documentosSeleccionados.map({\n                \"QuestionForm.useEffect.documentosValidados\": (doc)=>({\n                        ...doc,\n                        categoria: doc.categoria || null,\n                        numero_tema: doc.numero_tema !== undefined && doc.numero_tema !== null ? typeof doc.numero_tema === 'string' ? parseInt(doc.numero_tema, 10) : doc.numero_tema : undefined,\n                        // Asegurar que todos los campos opcionales estén presentes\n                        id: doc.id || undefined,\n                        creado_en: doc.creado_en || undefined,\n                        actualizado_en: doc.actualizado_en || undefined,\n                        user_id: doc.user_id || undefined,\n                        tipo_original: doc.tipo_original || undefined\n                    })\n            }[\"QuestionForm.useEffect.documentosValidados\"]);\n            setValue('documentos', documentosValidados);\n        }\n    }[\"QuestionForm.useEffect\"], [\n        documentosSeleccionados,\n        setValue\n    ]);\n    // Efecto para cargar la conversación activa al iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const cargarConversacionActiva = {\n                \"QuestionForm.useEffect.cargarConversacionActiva\": async ()=>{\n                    try {\n                        const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                        if (conversacionActiva) {\n                            setConversacionActualId(conversacionActiva.id);\n                            await cargarConversacion(conversacionActiva.id);\n                        } else {\n                            setMensajes([]);\n                            setConversacionActualId(null);\n                        }\n                    } catch (error) {\n                        console.warn('No se pudo cargar la conversación activa (esto es normal para usuarios nuevos):', error);\n                        // No mostrar error al usuario, simplemente inicializar sin conversación\n                        setMensajes([]);\n                        setConversacionActualId(null);\n                    }\n                }\n            }[\"QuestionForm.useEffect.cargarConversacionActiva\"];\n            cargarConversacionActiva();\n        }\n    }[\"QuestionForm.useEffect\"], []);\n    // Efecto para hacer scroll al último mensaje cuando se añade uno nuevo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"QuestionForm.useEffect\"], [\n        mensajes\n    ]);\n    // Función para cargar una conversación desde Supabase\n    const cargarConversacion = async (conversacionId)=>{\n        try {\n            setIsLoading(true);\n            // Activar la conversación seleccionada\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionId);\n            // Obtener los mensajes de la conversación\n            const mensajesDB = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId)(conversacionId);\n            // Convertir los mensajes de la base de datos al formato local\n            const mensajesFormateados = mensajesDB.map((msg)=>({\n                    id: msg.id,\n                    tipo: msg.tipo,\n                    contenido: msg.contenido,\n                    timestamp: new Date(msg.timestamp)\n                }));\n            // Actualizar el estado\n            setMensajes(mensajesFormateados);\n            setConversacionActualId(conversacionId);\n            setError('');\n        } catch (error) {\n            console.error('Error al cargar la conversación:', error);\n            setError('No se pudo cargar la conversación');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Función para guardar un mensaje en Supabase\n    const guardarMensajeEnDB = async (mensaje)=>{\n        try {\n            setGuardandoConversacion(true);\n            // Si no hay una conversación actual, crear una nueva\n            if (!conversacionActualId) {\n                // Solo crear una nueva conversación si es el primer mensaje del usuario\n                if (mensaje.tipo === 'usuario') {\n                    // Crear un título basado en la primera pregunta\n                    const titulo = \"Conversaci\\xf3n: \".concat(mensaje.contenido.substring(0, 50)).concat(mensaje.contenido.length > 50 ? '...' : '');\n                    // Crear una nueva conversación y marcarla como activa\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    // Guardar el ID de la conversación para futuros mensajes\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                } else {\n                    // Si es un mensaje de la IA pero no hay conversación actual,\n                    // algo salió mal. Intentar recuperar creando una nueva conversación.\n                    console.warn('No hay conversación actual para guardar el mensaje de la IA. Creando una nueva.');\n                    const titulo = 'Nueva conversación';\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                }\n            } else {\n                // Verificar que la conversación actual sigue siendo la activa\n                const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                if (!conversacionActiva || conversacionActiva.id !== conversacionActualId) {\n                    // Si la conversación actual no es la activa, activarla\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionActualId);\n                }\n                // Guardar el mensaje en la conversación existente\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                    conversacion_id: conversacionActualId,\n                    tipo: mensaje.tipo,\n                    contenido: mensaje.contenido\n                });\n            }\n        } catch (error) {\n            console.error('Error al guardar el mensaje:', error);\n        // No mostramos error al usuario para no interrumpir la experiencia\n        } finally{\n            setGuardandoConversacion(false);\n        }\n    };\n    // Función para iniciar una nueva conversación\n    const iniciarNuevaConversacion = async ()=>{\n        try {\n            setIsLoading(true);\n            // Desactivar todas las conversaciones en la base de datos\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones)();\n            // Limpiar los mensajes actuales\n            setMensajes([]);\n            // Establecer el ID de conversación a null para que se cree una nueva en el próximo mensaje\n            setConversacionActualId(null);\n            setError('');\n        } catch (error) {\n            console.error('Error al iniciar nueva conversación:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Función para manejar cuando se elimina una conversación\n    const handleConversationDeleted = ()=>{\n        // Si se eliminó la conversación actual, limpiar el estado\n        setMensajes([]);\n        setConversacionActualId(null);\n        setError('');\n    };\n    // Cambia handleSubmit para usar React Hook Form\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        setError('');\n        // Añadir la pregunta del usuario al historial\n        const preguntaUsuario = {\n            tipo: 'usuario',\n            contenido: data.pregunta,\n            timestamp: new Date()\n        };\n        setMensajes((prevMensajes)=>[\n                ...prevMensajes,\n                preguntaUsuario\n            ]);\n        setIsLoading(true);\n        setError('');\n        // Limpiar el campo de pregunta después de enviarla\n        reset({\n            pregunta: '',\n            documentos: documentosSeleccionados\n        });\n        try {\n            // Guardar la pregunta del usuario en Supabase\n            await guardarMensajeEnDB(preguntaUsuario);\n            // Pasar los documentos completos a la función obtenerRespuestaIA\n            // No solo el contenido, sino también el título, categoría y número de tema\n            // Obtener respuesta de la IA\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    pregunta: preguntaUsuario.contenido,\n                    documentos: data.documentos\n                })\n            });\n            const respuestaIA = await response.json();\n            let respuestaTexto = '';\n            if (respuestaIA.result) {\n                respuestaTexto = typeof respuestaIA.result === 'string' ? respuestaIA.result : JSON.stringify(respuestaIA.result);\n            } else if (respuestaIA.error) {\n                respuestaTexto = typeof respuestaIA.error === 'string' ? respuestaIA.error : JSON.stringify(respuestaIA.error);\n            } else {\n                respuestaTexto = 'Error desconocido al obtener respuesta de la IA.';\n            }\n            // Añadir la respuesta de la IA al historial\n            const mensajeIA = {\n                tipo: 'ia',\n                contenido: respuestaTexto,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeIA\n                ]);\n            // Guardar la respuesta de la IA en Supabase\n            await guardarMensajeEnDB(mensajeIA);\n            // Si es la primera pregunta, actualizar el título de la conversación con un título más descriptivo\n            if (mensajes.length === 0 && conversacionActualId) {\n                const tituloMejorado = \"Conversaci\\xf3n: \".concat(data.pregunta.substring(0, 50)).concat(data.pregunta.length > 50 ? '...' : '');\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion)(conversacionActualId, tituloMejorado);\n            }\n        } catch (error) {\n            console.error('Error al obtener respuesta:', error);\n            // Determinar el tipo de error y mostrar un mensaje más específico\n            let mensajeError = 'Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo.';\n            if (error instanceof Error) {\n                if (error.message.includes('API key')) {\n                    mensajeError = 'Error de configuración: La clave de API de Gemini no está configurada correctamente.';\n                } else if (error.message.includes('network') || error.message.includes('fetch')) {\n                    mensajeError = 'Error de conexión: No se pudo conectar con el servicio de IA. Verifica tu conexión a internet.';\n                } else if (error.message.includes('quota') || error.message.includes('limit')) {\n                    mensajeError = 'Se ha alcanzado el límite de uso del servicio de IA. Inténtalo más tarde.';\n                } else {\n                    mensajeError = \"Error: \".concat(error.message);\n                }\n            }\n            setError(mensajeError);\n            // Añadir mensaje de error como respuesta de la IA\n            const mensajeErrorIA = {\n                tipo: 'ia',\n                contenido: mensajeError,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeErrorIA\n                ]);\n            // Intentar guardar el mensaje de error en Supabase (sin fallar si no se puede)\n            try {\n                await guardarMensajeEnDB(mensajeErrorIA);\n            } catch (dbError) {\n                console.error('Error al guardar mensaje de error en DB:', dbError);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Formatear la fecha para mostrarla en el chat\n    const formatearFecha = (fecha)=>{\n        return fecha.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-6 flex h-[600px] gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: iniciarNuevaConversacion,\n                            className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center\",\n                            disabled: isLoading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 mr-2\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                \"Nueva conversaci\\xf3n\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50\",\n                        style: {\n                            height: 'calc(100% - 180px)'\n                        },\n                        children: mensajes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full text-gray-500\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Selecciona documentos y haz una pregunta para comenzar la conversaci\\xf3n.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                mensajes.map((mensaje, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(mensaje.tipo === 'usuario' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-[80%] p-3 rounded-lg \".concat(mensaje.tipo === 'usuario' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-white border border-gray-300 rounded-bl-none'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: mensaje.contenido\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs mt-1 text-right \".concat(mensaje.tipo === 'usuario' ? 'text-blue-100' : 'text-gray-500'),\n                                                    children: formatearFecha(mensaje.timestamp)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, mensaje.id || index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)),\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                                    style: {\n                                                        animationDelay: '0.2s'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                                    style: {\n                                                        animationDelay: '0.4s'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                guardandoConversacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500 text-center py-1\",\n                                    children: \"Guardando conversaci\\xf3n...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmitForm(onSubmit),\n                        className: \"mt-auto\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500 text-sm mb-2\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-600 mb-2\",\n                                children: documentosSeleccionados.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600\",\n                                    children: [\n                                        \"✓ \",\n                                        documentosSeleccionados.length,\n                                        \" documento\",\n                                        documentosSeleccionados.length !== 1 ? 's' : '',\n                                        \" seleccionado\",\n                                        documentosSeleccionados.length !== 1 ? 's' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600\",\n                                    children: \"⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"pregunta\",\n                                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                rows: 2,\n                                                ...register('pregunta'),\n                                                placeholder: \"Escribe tu pregunta sobre los documentos seleccionados...\",\n                                                disabled: isLoading,\n                                                onKeyDown: (e)=>{\n                                                    if (e.key === 'Enter' && !e.shiftKey) {\n                                                        e.preventDefault();\n                                                        handleSubmitForm(onSubmit)(); // Ejecutar la función devuelta\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.pregunta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-xs mt-1\",\n                                                children: errors.pregunta.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"Presiona Enter para enviar, Shift+Enter para nueva l\\xednea\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        disabled: isLoading || documentosSeleccionados.length === 0,\n                                        title: documentosSeleccionados.length === 0 ? 'Selecciona al menos un documento para hacer una pregunta' : 'Enviar pregunta',\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin h-5 w-5 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConversationSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSelectConversation: cargarConversacion,\n                conversacionActualId: conversacionActualId,\n                onConversationDeleted: handleConversationDeleted\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\conversations\\\\components\\\\QuestionForm.tsx\",\n        lineNumber: 352,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionForm, \"v9we2A8sthcTBYTGgANkp7ddalw=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = QuestionForm;\nvar _c;\n$RefreshReg$(_c, \"QuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\n"));

/***/ })

});