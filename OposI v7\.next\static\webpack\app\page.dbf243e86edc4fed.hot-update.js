"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiCheckSquare,FiChevronRight,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiRefreshCw,FiSettings,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticoSupabase__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../features/shared/components/DiagnosticoSupabase */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticoSupabase.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TabButton = (param)=>{\n    let { active, onClick, icon, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 5\n            }, undefined),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined);\n};\n_c = TabButton;\nfunction Home() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Hooks para el sistema de tareas en segundo plano\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_18__.useBackgroundGeneration)();\n    // Hook para manejar los resultados del plan de estudios\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_19__.usePlanEstudiosResults)({\n        onResult: {\n            \"Home.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"Home.usePlanEstudiosResults\"],\n        onError: {\n            \"Home.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"Home.usePlanEstudiosResults\"]\n    });\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Cargar datos del temario y verificar planificación\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const cargarDatosTemario = {\n                \"Home.useEffect.cargarDatosTemario\": async ()=>{\n                    if (!user) return;\n                    try {\n                        const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_20__.obtenerTemarioUsuario)();\n                        if (temario) {\n                            setTemarioId(temario.id);\n                            const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_21__.tienePlanificacionConfigurada)(temario.id);\n                            setTienePlanificacion(tienePlan);\n                            // Verificar si ya existe un plan de estudios guardado\n                            const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_22__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                            if (planExistente && planExistente.plan_data) {\n                                console.log('✅ Plan de estudios existente encontrado');\n                                setPlanEstudios(planExistente.plan_data);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error al cargar datos del temario:', error);\n                    }\n                }\n            }[\"Home.useEffect.cargarDatosTemario\"];\n            cargarDatosTemario();\n        }\n    }[\"Home.useEffect\"], [\n        user\n    ]);\n    // Actualizar el plan cuando se reciba un resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        latestResult\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        // Recargar la lista de documentos automáticamente\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        // Ocultar el mensaje después de 5 segundos\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        // Recargar la lista de documentos cuando se elimina uno\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>{\n                    setPlanEstudios(result);\n                },\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        // Convertir el plan estructurado a texto\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              // Convertir markdown a HTML b\\xe1sico para impresi\\xf3n\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const tabs = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiRefreshCw, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 50\n            }, this),\n            color: 'bg-gradient-to-r from-blue-600 to-purple-600'\n        },\n        {\n            id: 'temario',\n            label: 'Mi Temario',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 49\n            }, this),\n            color: 'bg-green-600'\n        },\n        {\n            id: 'planEstudios',\n            label: 'Mi Plan de Estudios',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCalendar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 63\n            }, this),\n            color: 'bg-teal-600'\n        },\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiMessageSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiLayers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiFileText, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCheckSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        },\n        {\n            id: 'gestionar',\n            label: 'Gestionar Documentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiSettings, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 61\n            }, this),\n            color: 'bg-gray-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xfa de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    ref: documentSelectorRef,\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onNavigateToTab: setActiveTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                                children: \"Mi Plan de Estudios\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleGenerarPlanEstudios,\n                                                                    disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                    className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiRefreshCw, {\n                                                                            className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"Regenerar Plan\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                children: \"Generando tu plan personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 25\n                                                    }, this) : planEstudios ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        plan: planEstudios,\n                                                        temarioId: temarioId || ''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCalendar, {\n                                                                    className: \"w-10 h-10 text-teal-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                children: \"Genera tu Plan de Estudios Personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleGenerarPlanEstudios,\n                                                                disabled: !tienePlanificacion,\n                                                                className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCalendar, {\n                                                                        className: \"w-5 h-5 mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Generar Plan de Estudios\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-red-600 mt-4\",\n                                                                children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'diagnostico' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticoSupabase__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 503,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"tt7/fuPqc40125gJnIme7W7nT0k=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_18__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_19__.usePlanEstudiosResults\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});