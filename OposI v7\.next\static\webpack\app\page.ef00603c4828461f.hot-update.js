"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiCheckSquare,FiChevronRight,FiDownload,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiPrinter,FiRefreshCw,FiSettings,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TabButton = (param)=>{\n    let { active, onClick, icon, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 5\n            }, undefined),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 3\n    }, undefined);\n};\n_c = TabButton;\nfunction Home() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Hooks para el sistema de tareas en segundo plano\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__.useBackgroundGeneration)();\n    // Hook para manejar los resultados del plan de estudios\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_18__.usePlanEstudiosResults)({\n        onResult: {\n            \"Home.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"Home.usePlanEstudiosResults\"],\n        onError: {\n            \"Home.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"Home.usePlanEstudiosResults\"]\n    });\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{}\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Cargar datos del temario y verificar planificación\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const cargarDatosTemario = {\n                \"Home.useEffect.cargarDatosTemario\": async ()=>{\n                    if (!user) return;\n                    try {\n                        const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_19__.obtenerTemarioUsuario)();\n                        if (temario) {\n                            setTemarioId(temario.id);\n                            const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_20__.tienePlanificacionConfigurada)(temario.id);\n                            setTienePlanificacion(tienePlan);\n                            // Verificar si ya existe un plan de estudios guardado\n                            const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_21__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                            if (planExistente && planExistente.plan_data) {\n                                setPlanEstudios(planExistente.plan_data);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error al cargar datos del temario:', error);\n                    }\n                }\n            }[\"Home.useEffect.cargarDatosTemario\"];\n            cargarDatosTemario();\n        }\n    }[\"Home.useEffect\"], [\n        user\n    ]);\n    // Actualizar el plan cuando se reciba un resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        latestResult\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        // Recargar la lista de documentos automáticamente\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        // Ocultar el mensaje después de 5 segundos\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        // Recargar la lista de documentos cuando se elimina uno\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>{\n                    setPlanEstudios(result);\n                },\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        // Convertir el plan estructurado a texto\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              // Convertir markdown a HTML b\\xe1sico para impresi\\xf3n\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const tabs = [\n        {\n            id: 'dashboard',\n            label: 'Principal',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiRefreshCw, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 50\n            }, this),\n            color: 'bg-gradient-to-r from-blue-600 to-purple-600'\n        },\n        {\n            id: 'temario',\n            label: 'Mi Temario',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 49\n            }, this),\n            color: 'bg-green-600'\n        },\n        {\n            id: 'planEstudios',\n            label: 'Mi Plan de Estudios',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCalendar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 63\n            }, this),\n            color: 'bg-teal-600'\n        },\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiMessageSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiLayers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiFileText, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCheckSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        },\n        {\n            id: 'gestionar',\n            label: 'Gestionar Documentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiSettings, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 61\n            }, this),\n            color: 'bg-gray-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xfa de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    ref: documentSelectorRef,\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onNavigateToTab: setActiveTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                                children: \"Mi Plan de Estudios\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleGenerarPlanEstudios,\n                                                                            disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                            className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiRefreshCw, {\n                                                                                    className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 421,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Regenerar Plan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleDescargarPlan,\n                                                                            className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiDownload, {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 428,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Descargar\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleImprimirPlan,\n                                                                            className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiPrinter, {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Imprimir\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                children: \"Generando tu plan personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 25\n                                                    }, this) : planEstudios ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        plan: planEstudios,\n                                                        temarioId: temarioId || ''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCalendar, {\n                                                                    className: \"w-10 h-10 text-teal-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                children: \"Genera tu Plan de Estudios Personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleGenerarPlanEstudios,\n                                                                disabled: !tienePlanificacion,\n                                                                className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCalendar, {\n                                                                        className: \"w-5 h-5 mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Generar Plan de Estudios\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-red-600 mt-4\",\n                                                                children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"tt7/fuPqc40125gJnIme7W7nT0k=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_18__.usePlanEstudiosResults\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx":
/*!**********************************************************************!*\
  !*** ./src/features/planificacion/components/PlanEstudiosViewer.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiClock,FiRefreshCw,FiTarget!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst PlanEstudiosViewer = (param)=>{\n    let { plan, temarioId } = param;\n    _s();\n    const [progresoPlan, setProgresoPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [planId, setPlanId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlanEstudiosViewer.useEffect\": ()=>{\n            cargarProgreso();\n        }\n    }[\"PlanEstudiosViewer.useEffect\"], [\n        temarioId\n    ]);\n    const cargarProgreso = async ()=>{\n        try {\n            // Obtener el plan activo\n            const planActivo = await (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_2__.obtenerPlanEstudiosActivoCliente)(temarioId);\n            if (!planActivo) {\n                console.warn('No se encontró plan activo para el temario');\n                setIsLoading(false);\n                return;\n            }\n            setPlanId(planActivo.id);\n            // Obtener el progreso del plan\n            const progreso = await (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_2__.obtenerProgresoPlaneCliente)(planActivo.id);\n            setProgresoPlan(progreso);\n        } catch (error) {\n            console.error('Error al cargar progreso:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleTareaCompletada = async (tarea, semanaNum, dia)=>{\n        if (!planId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('No se pudo identificar el plan de estudios');\n            return;\n        }\n        try {\n            // Verificar si la tarea ya está completada\n            const tareaExistente = progresoPlan.find((p)=>p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo);\n            const nuevoEstado = !(tareaExistente === null || tareaExistente === void 0 ? void 0 : tareaExistente.completado);\n            // Guardar el progreso usando el nuevo servicio\n            const exito = await (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_2__.guardarProgresoTareaCliente)(planId, semanaNum, dia, tarea.titulo, tarea.tipo, nuevoEstado);\n            if (exito) {\n                // Actualizar el estado local\n                setProgresoPlan((prev)=>{\n                    const index = prev.findIndex((p)=>p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo);\n                    if (index >= 0) {\n                        // Actualizar existente\n                        const updated = [\n                            ...prev\n                        ];\n                        updated[index] = {\n                            ...updated[index],\n                            completado: nuevoEstado,\n                            fecha_completado: nuevoEstado ? new Date().toISOString() : undefined\n                        };\n                        return updated;\n                    } else {\n                        // Crear nuevo registro\n                        return [\n                            ...prev,\n                            {\n                                id: \"temp-\".concat(Date.now()),\n                                plan_id: planId,\n                                user_id: '',\n                                semana_numero: semanaNum,\n                                dia_nombre: dia,\n                                tarea_titulo: tarea.titulo,\n                                tarea_tipo: tarea.tipo,\n                                completado: nuevoEstado,\n                                fecha_completado: nuevoEstado ? new Date().toISOString() : undefined,\n                                creado_en: new Date().toISOString(),\n                                actualizado_en: new Date().toISOString()\n                            }\n                        ];\n                    }\n                });\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(nuevoEstado ? 'Tarea completada' : 'Tarea marcada como pendiente');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Error al actualizar el progreso');\n            }\n        } catch (error) {\n            console.error('Error al actualizar tarea:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Error al actualizar el progreso');\n        }\n    };\n    const estaCompletada = (tarea, semanaNum, dia)=>{\n        return progresoPlan.some((p)=>p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo && p.completado);\n    };\n    const calcularProgreso = ()=>{\n        const totalTareas = plan.semanas.reduce((acc, semana)=>acc + semana.dias.reduce((dayAcc, dia)=>dayAcc + dia.tareas.length, 0), 0);\n        const tareasCompletadasCount = progresoPlan.filter((p)=>p.completado).length;\n        return {\n            completadas: tareasCompletadasCount,\n            total: totalTareas,\n            porcentaje: totalTareas > 0 ? Math.round(tareasCompletadasCount / totalTareas * 100) : 0\n        };\n    };\n    const progreso = calcularProgreso();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-3 text-gray-600\",\n                    children: \"Cargando progreso...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n            lineNumber: 147,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"Introducci\\xf3n\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-800\",\n                        children: plan.introduccion\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Progreso General\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: [\n                                    progreso.porcentaje,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(progreso.porcentaje, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            progreso.completadas,\n                            \" de \",\n                            progreso.total,\n                            \" tareas completadas\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiClock, {\n                                    className: \"w-5 h-5 text-blue-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Tiempo Total\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.tiempoTotalEstudio\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBook, {\n                                    className: \"w-5 h-5 text-green-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Temas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.numeroTemas\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTarget, {\n                                    className: \"w-5 h-5 text-purple-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Estudio Nuevo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.duracionEstudioNuevo\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiRefreshCw, {\n                                    className: \"w-5 h-5 text-orange-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Repaso Final\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.duracionRepasoFinal\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCalendar, {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Cronograma Semanal\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined),\n                    plan.semanas.map((semana, semanaIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 px-6 py-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: [\n                                                        \"Semana \",\n                                                        semana.numero\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        semana.fechaInicio,\n                                                        \" - \",\n                                                        semana.fechaFin\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mt-2\",\n                                            children: semana.objetivoPrincipal\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 space-y-4\",\n                                    children: semana.dias.map((dia, diaIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-100 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: dia.dia\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                                            children: [\n                                                                dia.horas,\n                                                                \"h\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: dia.tareas.map((tarea, tareaIndex)=>{\n                                                        const completada = estaCompletada(tarea, semana.numero, dia.dia);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start p-3 rounded-lg border transition-all cursor-pointer \".concat(completada ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200 hover:border-blue-300'),\n                                                            onClick: ()=>toggleTareaCompletada(tarea, semana.numero, dia.dia),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center \".concat(completada ? 'bg-green-500 border-green-500' : 'border-gray-300 hover:border-blue-400'),\n                                                                    children: completada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCheck, {\n                                                                        className: \"w-3 h-3 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 44\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                            className: \"font-medium \".concat(completada ? 'text-green-800 line-through' : 'text-gray-900'),\n                                                                            children: tarea.titulo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                                            lineNumber: 272,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        tarea.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm mt-1 \".concat(completada ? 'text-green-700' : 'text-gray-600'),\n                                                                            children: tarea.descripcion\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mt-2 space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded \".concat(tarea.tipo === 'estudio' ? 'bg-blue-100 text-blue-800' : tarea.tipo === 'repaso' ? 'bg-yellow-100 text-yellow-800' : tarea.tipo === 'practica' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: tarea.tipo\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500\",\n                                                                                    children: tarea.duracionEstimada\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                                                    lineNumber: 289,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, tareaIndex, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, diaIndex, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, semanaIndex, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-yellow-900 mb-2\",\n                        children: \"Estrategia de Repasos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-800\",\n                        children: typeof plan.estrategiaRepasos === 'string' ? plan.estrategiaRepasos : plan.estrategiaRepasos.descripcion || 'Estrategia de repasos no disponible'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-purple-900 mb-2\",\n                        children: \"Pr\\xf3ximos Pasos y Consejos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-purple-800\",\n                        children: typeof plan.proximosPasos === 'string' ? plan.proximosPasos : plan.proximosPasos.descripcion || 'Próximos pasos no disponibles'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlanEstudiosViewer, \"+wNfwVFSjpH9mnGKYLxCpLXb/yQ=\");\n_c = PlanEstudiosViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlanEstudiosViewer);\nvar _c;\n$RefreshReg$(_c, \"PlanEstudiosViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts":
/*!**************************************************************************!*\
  !*** ./src/features/planificacion/services/planEstudiosClientService.ts ***!
  \**************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   guardarProgresoTareaCliente: () => (/* binding */ guardarProgresoTareaCliente),\n/* harmony export */   obtenerEstadisticasProgresoCliente: () => (/* binding */ obtenerEstadisticasProgresoCliente),\n/* harmony export */   obtenerPlanEstudiosActivoCliente: () => (/* binding */ obtenerPlanEstudiosActivoCliente),\n/* harmony export */   obtenerProgresoPlaneCliente: () => (/* binding */ obtenerProgresoPlaneCliente)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(app-pages-browser)/./src/features/auth/services/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ obtenerPlanEstudiosActivoCliente,obtenerProgresoPlaneCliente,guardarProgresoTareaCliente,obtenerEstadisticasProgresoCliente auto */ \n\n/**\n * Obtiene el plan de estudios activo para un temario (versión para cliente)\n */ async function obtenerPlanEstudiosActivoCliente(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('*').eq('user_id', user.id).eq('temario_id', temarioId).eq('activo', true).single();\n        if (error) {\n            if (error.code === 'PGRST116') {\n                return null; // No hay plan activo\n            }\n            console.error('Error al obtener plan activo:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener plan activo:', error);\n        return null;\n    }\n}\n/**\n * Obtiene el progreso de un plan de estudios (versión para cliente)\n */ async function obtenerProgresoPlaneCliente(planId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('*').eq('plan_id', planId).eq('user_id', user.id).order('semana_numero', {\n            ascending: true\n        }).order('creado_en', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener progreso del plan:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener progreso del plan:', error);\n        return [];\n    }\n}\n/**\n * Guarda el progreso de una tarea del plan (versión para cliente)\n */ async function guardarProgresoTareaCliente(planId, semanaNúmero, diaNombre, tareaTitulo, tareaTipo, completado, tiempoRealMinutos, notasProgreso, calificacion) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return false;\n        }\n        // Verificar si ya existe un registro de progreso para esta tarea\n        const { data: existente } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('id').eq('plan_id', planId).eq('user_id', user.id).eq('semana_numero', semanaNúmero).eq('dia_nombre', diaNombre).eq('tarea_titulo', tareaTitulo).single();\n        if (existente) {\n            // Actualizar registro existente\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').update({\n                completado,\n                fecha_completado: completado ? new Date().toISOString() : null,\n                tiempo_real_minutos: tiempoRealMinutos,\n                notas_progreso: notasProgreso,\n                calificacion,\n                actualizado_en: new Date().toISOString()\n            }).eq('id', existente.id);\n            if (error) {\n                console.error('Error al actualizar progreso:', error);\n                return false;\n            }\n        } else {\n            // Crear nuevo registro\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').insert([\n                {\n                    plan_id: planId,\n                    user_id: user.id,\n                    semana_numero: semanaNúmero,\n                    dia_nombre: diaNombre,\n                    tarea_titulo: tareaTitulo,\n                    tarea_tipo: tareaTipo,\n                    completado,\n                    fecha_completado: completado ? new Date().toISOString() : null,\n                    tiempo_real_minutos: tiempoRealMinutos,\n                    notas_progreso: notasProgreso,\n                    calificacion\n                }\n            ]);\n            if (error) {\n                console.error('Error al crear progreso:', error);\n                return false;\n            }\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al guardar progreso de tarea:', error);\n        return false;\n    }\n}\n/**\n * Obtiene estadísticas del progreso del plan (versión para cliente)\n */ async function obtenerEstadisticasProgresoCliente(planId) {\n    try {\n        const progreso = await obtenerProgresoPlaneCliente(planId);\n        const plan = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('plan_data').eq('id', planId).single();\n        if (!plan.data) {\n            return {\n                totalTareas: 0,\n                tareasCompletadas: 0,\n                porcentajeCompletado: 0,\n                tiempoTotalEstimado: 0,\n                tiempoTotalReal: 0,\n                semanasCompletadas: 0,\n                totalSemanas: 0\n            };\n        }\n        const planData = plan.data.plan_data;\n        const totalSemanas = planData.semanas.length;\n        // Calcular total de tareas\n        let totalTareas = 0;\n        planData.semanas.forEach((semana)=>{\n            semana.dias.forEach((dia)=>{\n                totalTareas += dia.tareas.length;\n            });\n        });\n        const tareasCompletadas = progreso.filter((p)=>p.completado).length;\n        const porcentajeCompletado = totalTareas > 0 ? tareasCompletadas / totalTareas * 100 : 0;\n        const tiempoTotalReal = progreso.filter((p)=>p.tiempo_real_minutos).reduce((total, p)=>total + (p.tiempo_real_minutos || 0), 0);\n        // Calcular semanas completadas (todas las tareas de la semana completadas)\n        let semanasCompletadas = 0;\n        planData.semanas.forEach((semana)=>{\n            const tareasSemanaTotales = semana.dias.reduce((total, dia)=>total + dia.tareas.length, 0);\n            const tareasSemanCompletadas = progreso.filter((p)=>p.semana_numero === semana.numero && p.completado).length;\n            if (tareasSemanaTotales > 0 && tareasSemanCompletadas === tareasSemanaTotales) {\n                semanasCompletadas++;\n            }\n        });\n        return {\n            totalTareas,\n            tareasCompletadas,\n            porcentajeCompletado: Math.round(porcentajeCompletado * 100) / 100,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal,\n            semanasCompletadas,\n            totalSemanas\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas de progreso:', error);\n        return {\n            totalTareas: 0,\n            tareasCompletadas: 0,\n            porcentajeCompletado: 0,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal: 0,\n            semanasCompletadas: 0,\n            totalSemanas: 0\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx":
/*!************************************************************!*\
  !*** ./src/features/temario/components/TemarioManager.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiEdit,FiSettings,FiTrendingUp,FiZap!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _features_planificacion_components_PlanificacionAsistente__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/planificacion/components/PlanificacionAsistente */ \"(app-pages-browser)/./src/features/planificacion/components/PlanificacionAsistente.tsx\");\n/* harmony import */ var _TemarioEditModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TemarioEditModal */ \"(app-pages-browser)/./src/features/temario/components/TemarioEditModal.tsx\");\n/* harmony import */ var _TemaEditModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemaEditModal */ \"(app-pages-browser)/./src/features/temario/components/TemaEditModal.tsx\");\n/* harmony import */ var _TemaActions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TemaActions */ \"(app-pages-browser)/./src/features/temario/components/TemaActions.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TemarioManager = ()=>{\n    _s();\n    const [temario, setTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temas, setTemas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [actualizandoTema, setActualizandoTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarAsistentePlanificacion, setMostrarAsistentePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarModalEdicion, setMostrarModalEdicion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarModalEdicionTema, setMostrarModalEdicionTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [temaSeleccionado, setTemaSeleccionado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TemarioManager.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"TemarioManager.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            const temarioData = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemarioUsuario)();\n            if (temarioData) {\n                setTemario(temarioData);\n                const [temasData, estadisticasData, planificacionConfigurada] = await Promise.all([\n                    (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemas)(temarioData.id),\n                    (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temarioData.id),\n                    (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.tienePlanificacionConfigurada)(temarioData.id)\n                ]);\n                setTemas(temasData);\n                setEstadisticas(estadisticasData);\n                setTienePlanificacion(planificacionConfigurada);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del temario:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al cargar el temario');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleToggleCompletado = async (temaId, completado)=>{\n        setActualizandoTema(temaId);\n        try {\n            const exito = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.actualizarEstadoTema)(temaId, !completado);\n            if (exito) {\n                // Actualizar el estado local\n                setTemas(temas.map((tema)=>tema.id === temaId ? {\n                        ...tema,\n                        completado: !completado,\n                        fecha_completado: !completado ? new Date().toISOString() : undefined\n                    } : tema));\n                // Recalcular estadísticas\n                if (temario) {\n                    const nuevasEstadisticas = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temario.id);\n                    setEstadisticas(nuevasEstadisticas);\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success(!completado ? 'Tema marcado como completado' : 'Tema marcado como pendiente');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al actualizar el estado del tema');\n            }\n        } catch (error) {\n            console.error('Error al actualizar tema:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al actualizar el tema');\n        } finally{\n            setActualizandoTema(null);\n        }\n    };\n    const formatearFecha = (fecha)=>{\n        return new Date(fecha).toLocaleDateString('es-ES', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric'\n        });\n    };\n    const handleIniciarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(true);\n    };\n    const handleModificarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(true);\n    };\n    const handleCompletarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(false);\n        setTienePlanificacion(true);\n        // Recargar datos para reflejar los cambios\n        cargarDatos();\n    };\n    const handleCancelarPlanificacion = ()=>{\n        setMostrarAsistentePlanificacion(false);\n    };\n    const handleEditarTemario = ()=>{\n        setMostrarModalEdicion(true);\n    };\n    const handleGuardarTemario = (temarioActualizado)=>{\n        setTemario(temarioActualizado);\n        setMostrarModalEdicion(false);\n    };\n    const handleCancelarEdicion = ()=>{\n        setMostrarModalEdicion(false);\n    };\n    const handleEditarTema = (tema)=>{\n        setTemaSeleccionado(tema);\n        setMostrarModalEdicionTema(true);\n    };\n    const handleGuardarTema = (temaActualizado)=>{\n        setTemas(temas.map((tema)=>tema.id === temaActualizado.id ? temaActualizado : tema));\n        setMostrarModalEdicionTema(false);\n        setTemaSeleccionado(null);\n    };\n    const handleCancelarEdicionTema = ()=>{\n        setMostrarModalEdicionTema(false);\n        setTemaSeleccionado(null);\n    };\n    const handleEliminarTema = async (temaId)=>{\n        // Eliminar tema del estado local\n        setTemas(temas.filter((tema)=>tema.id !== temaId));\n        // Recalcular estadísticas\n        if (temario) {\n            const nuevasEstadisticas = await (0,_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTemario)(temario.id);\n            setEstadisticas(nuevasEstadisticas);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!temario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiBook, {\n                    className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No hay temario configurado\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"Configura tu temario desde el dashboard para comenzar.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar asistente de planificación si está activo\n    if (mostrarAsistentePlanificacion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanificacionAsistente__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            temario: temario,\n            onComplete: handleCompletarPlanificacion,\n            onCancel: handleCancelarPlanificacion,\n            isEditing: tienePlanificacion\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: temario.titulo\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    temario.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: temario.descripcion\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(temario.tipo === 'completo' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'),\n                                                children: temario.tipo === 'completo' ? 'Temario Completo' : 'Temas Sueltos'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"Creado el \",\n                                                    formatearFecha(temario.creado_en)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleEditarTemario,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors\",\n                                    title: \"Editar temario\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiEdit, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiBook, {\n                                            className: \"w-5 h-5 text-blue-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Total Temas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: estadisticas.totalTemas\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                            className: \"w-5 h-5 text-green-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"Completados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: estadisticas.temasCompletados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiTrendingUp, {\n                                            className: \"w-5 h-5 text-purple-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-purple-800\",\n                                                    children: \"Progreso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: [\n                                                        estadisticas.porcentajeCompletado.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined),\n                    estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Progreso del temario\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            estadisticas.porcentajeCompletado.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: \"\".concat(estadisticas.porcentajeCompletado, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            temario.tipo === 'completo' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-xl p-6 \".concat(tienePlanificacion ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                tienePlanificacion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                    className: \"w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiZap, {\n                                    className: \"w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2 \".concat(tienePlanificacion ? 'text-green-900' : 'text-blue-900'),\n                                            children: tienePlanificacion ? 'Planificación Configurada' : 'Planificación Inteligente con IA'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mb-3 \".concat(tienePlanificacion ? 'text-green-800' : 'text-blue-800'),\n                                            children: tienePlanificacion ? 'Ya tienes configurada tu planificación de estudio personalizada. Pronto podrás ver tu calendario y seguimiento.' : 'Configura tu planificación personalizada con nuestro asistente inteligente:'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-blue-800 text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Planificaci\\xf3n autom\\xe1tica de estudio con IA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Seguimiento de progreso personalizado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Recomendaciones de orden de estudio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Estimaci\\xf3n de tiempos por tema\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: !tienePlanificacion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleIniciarPlanificacion,\n                                className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiZap, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Configurar Planificaci\\xf3n\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModificarPlanificacion,\n                                className: \"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiSettings, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Modificar Planificaci\\xf3n\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Temas del Temario\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Marca los temas como completados seg\\xfan vayas estudi\\xe1ndolos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: temas.map((tema)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium\",\n                                                        children: tema.numero\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium \".concat(tema.completado ? 'text-gray-500 line-through' : 'text-gray-900'),\n                                                            children: tema.titulo\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        tema.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: tema.descripcion\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        tema.fecha_completado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2 text-sm text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiEdit_FiSettings_FiTrendingUp_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_9__.FiCheck, {\n                                                                    className: \"w-4 h-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"Completado el \",\n                                                                formatearFecha(tema.fecha_completado)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemaActions__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                tema: tema,\n                                                onEdit: handleEditarTema,\n                                                onDelete: handleEliminarTema,\n                                                onToggleCompletado: handleToggleCompletado,\n                                                isUpdating: actualizandoTema === tema.id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tema.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined),\n            temario && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemarioEditModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: mostrarModalEdicion,\n                onClose: handleCancelarEdicion,\n                temario: temario,\n                onSave: handleGuardarTemario\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, undefined),\n            temaSeleccionado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemaEditModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: mostrarModalEdicionTema,\n                onClose: handleCancelarEdicionTema,\n                tema: temaSeleccionado,\n                onSave: handleGuardarTema\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n                lineNumber: 405,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\temario\\\\components\\\\TemarioManager.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemarioManager, \"Jx9P86PceqgdNawpu1osiXuX0v4=\");\n_c = TemarioManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemarioManager);\nvar _c;\n$RefreshReg$(_c, \"TemarioManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts":
/*!*********************************************!*\
  !*** ./src/hooks/usePlanEstudiosResults.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePlanEstudiosResults: () => (/* binding */ usePlanEstudiosResults)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(app-pages-browser)/./src/contexts/BackgroundTasksContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ usePlanEstudiosResults auto */ \n\nconst usePlanEstudiosResults = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { tasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__.useBackgroundTasks)();\n    const [latestResult, setLatestResult] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePlanEstudiosResults.useEffect\": ()=>{\n            const planEstudiosTasks = tasks.filter({\n                \"usePlanEstudiosResults.useEffect.planEstudiosTasks\": (task)=>task.type === 'plan-estudios'\n            }[\"usePlanEstudiosResults.useEffect.planEstudiosTasks\"]);\n            // Verificar si hay tareas activas\n            const hasActiveTasks = planEstudiosTasks.some({\n                \"usePlanEstudiosResults.useEffect.hasActiveTasks\": (task)=>task.status === 'pending' || task.status === 'processing'\n            }[\"usePlanEstudiosResults.useEffect.hasActiveTasks\"]);\n            setIsLoading(hasActiveTasks);\n            // Buscar la tarea completada más reciente\n            const completedTasks = planEstudiosTasks.filter({\n                \"usePlanEstudiosResults.useEffect.completedTasks\": (task)=>task.status === 'completed' && task.result\n            }[\"usePlanEstudiosResults.useEffect.completedTasks\"]).sort({\n                \"usePlanEstudiosResults.useEffect.completedTasks\": (a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n            }[\"usePlanEstudiosResults.useEffect.completedTasks\"]);\n            if (completedTasks.length > 0) {\n                const latestTask = completedTasks[0];\n                setLatestResult(latestTask.result);\n                // Ejecutar callback si es la primera vez que vemos este resultado\n                if (options.onResult && latestTask.result !== latestResult) {\n                    options.onResult(latestTask.result);\n                }\n            }\n            // Buscar tareas con error más recientes\n            const errorTasks = planEstudiosTasks.filter({\n                \"usePlanEstudiosResults.useEffect.errorTasks\": (task)=>task.status === 'error'\n            }[\"usePlanEstudiosResults.useEffect.errorTasks\"]).sort({\n                \"usePlanEstudiosResults.useEffect.errorTasks\": (a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n            }[\"usePlanEstudiosResults.useEffect.errorTasks\"]);\n            if (errorTasks.length > 0 && options.onError) {\n                const latestErrorTask = errorTasks[0];\n                options.onError(latestErrorTask.error || 'Error desconocido');\n            }\n        }\n    }[\"usePlanEstudiosResults.useEffect\"], [\n        tasks,\n        options,\n        latestResult\n    ]);\n    return {\n        latestResult,\n        isLoading,\n        hasResults: !!latestResult\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\n"));

/***/ })

});