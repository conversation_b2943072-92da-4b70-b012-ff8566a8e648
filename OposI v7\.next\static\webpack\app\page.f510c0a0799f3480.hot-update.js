"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiCheckSquare,FiChevronRight,FiDownload,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiPrinter,FiRefreshCw,FiSettings,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TabButton = (param)=>{\n    let { active, onClick, icon, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 5\n            }, undefined),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 3\n    }, undefined);\n};\n_c = TabButton;\nfunction Home() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Hooks para el sistema de tareas en segundo plano\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__.useBackgroundGeneration)();\n    // Hook para manejar los resultados del plan de estudios\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_18__.usePlanEstudiosResults)({\n        onResult: {\n            \"Home.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"Home.usePlanEstudiosResults\"],\n        onError: {\n            \"Home.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"Home.usePlanEstudiosResults\"]\n    });\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Cargar datos del temario y verificar planificación\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const cargarDatosTemario = {\n                \"Home.useEffect.cargarDatosTemario\": async ()=>{\n                    if (!user) return;\n                    try {\n                        const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_19__.obtenerTemarioUsuario)();\n                        if (temario) {\n                            setTemarioId(temario.id);\n                            const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_20__.tienePlanificacionConfigurada)(temario.id);\n                            setTienePlanificacion(tienePlan);\n                            // Verificar si ya existe un plan de estudios guardado\n                            const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_21__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                            if (planExistente && planExistente.plan_data) {\n                                setPlanEstudios(planExistente.plan_data);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error al cargar datos del temario:', error);\n                    }\n                }\n            }[\"Home.useEffect.cargarDatosTemario\"];\n            cargarDatosTemario();\n        }\n    }[\"Home.useEffect\"], [\n        user\n    ]);\n    // Actualizar el plan cuando se reciba un resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        latestResult\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        // Recargar la lista de documentos automáticamente\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        // Ocultar el mensaje después de 5 segundos\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        // Recargar la lista de documentos cuando se elimina uno\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>{\n                    setPlanEstudios(result);\n                },\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        // Convertir el plan estructurado a texto\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_23__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              // Convertir markdown a HTML b\\xe1sico para impresi\\xf3n\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const tabs = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiRefreshCw, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 50\n            }, this),\n            color: 'bg-gradient-to-r from-blue-600 to-purple-600'\n        },\n        {\n            id: 'temario',\n            label: 'Mi Temario',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 49\n            }, this),\n            color: 'bg-green-600'\n        },\n        {\n            id: 'planEstudios',\n            label: 'Mi Plan de Estudios',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCalendar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 63\n            }, this),\n            color: 'bg-teal-600'\n        },\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiMessageSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiLayers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiFileText, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCheckSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        },\n        {\n            id: 'gestionar',\n            label: 'Gestionar Documentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiSettings, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 61\n            }, this),\n            color: 'bg-gray-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xfa de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    ref: documentSelectorRef,\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onNavigateToTab: setActiveTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                                children: \"Mi Plan de Estudios\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleGenerarPlanEstudios,\n                                                                            disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                            className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiRefreshCw, {\n                                                                                    className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 421,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Regenerar Plan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleDescargarPlan,\n                                                                            className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiDownload, {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 428,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Descargar\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleImprimirPlan,\n                                                                            className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiPrinter, {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Imprimir\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                children: \"Generando tu plan personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 25\n                                                    }, this) : planEstudios ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        plan: planEstudios,\n                                                        temarioId: temarioId || ''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCalendar, {\n                                                                    className: \"w-10 h-10 text-teal-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                children: \"Genera tu Plan de Estudios Personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleGenerarPlanEstudios,\n                                                                disabled: !tienePlanificacion,\n                                                                className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiCalendar, {\n                                                                        className: \"w-5 h-5 mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Generar Plan de Estudios\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-red-600 mt-4\",\n                                                                children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"tt7/fuPqc40125gJnIme7W7nT0k=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_18__.usePlanEstudiosResults\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});