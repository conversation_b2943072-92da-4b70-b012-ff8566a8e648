"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiCheckSquare,FiChevronRight,FiDownload,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiPrinter,FiRefreshCw,FiSettings,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import { usePlanEstudiosResults } from '@/hooks/usePlanEstudiosResults';\n\n\n// import { PlanEstudiosEstructurado } from '@/features/planificacion/services/planGeneratorService';\n// import { obtenerPlanEstudiosActivoCliente } from '@/features/planificacion/services/planEstudiosClientService';\n// import PlanEstudiosViewer from '@/features/planificacion/components/PlanEstudiosViewer';\n\nconst TabButton = (param)=>{\n    let { active, onClick, icon, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 5\n            }, undefined),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 3\n    }, undefined);\n};\n_c = TabButton;\nfunction Home() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Hooks para el sistema de tareas en segundo plano\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__.useBackgroundGeneration)();\n    // Hook para manejar los resultados del plan de estudios\n    const { latestResult, isLoading: isPlanLoading } = usePlanEstudiosResults({\n        onResult: {\n            \"Home.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_20__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"Home.usePlanEstudiosResults\"],\n        onError: {\n            \"Home.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_20__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"Home.usePlanEstudiosResults\"]\n    });\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{}\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Cargar datos del temario y verificar planificación\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const cargarDatosTemario = {\n                \"Home.useEffect.cargarDatosTemario\": async ()=>{\n                    if (!user) return;\n                    try {\n                        const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_18__.obtenerTemarioUsuario)();\n                        if (temario) {\n                            setTemarioId(temario.id);\n                            const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_19__.tienePlanificacionConfigurada)(temario.id);\n                            setTienePlanificacion(tienePlan);\n                            // Verificar si ya existe un plan de estudios guardado\n                            const planExistente = await obtenerPlanEstudiosActivoCliente(temario.id);\n                            if (planExistente && planExistente.plan_data) {\n                                setPlanEstudios(planExistente.plan_data);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error al cargar datos del temario:', error);\n                    }\n                }\n            }[\"Home.useEffect.cargarDatosTemario\"];\n            cargarDatosTemario();\n        }\n    }[\"Home.useEffect\"], [\n        user\n    ]);\n    // Actualizar el plan cuando se reciba un resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        latestResult\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        // Recargar la lista de documentos automáticamente\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        // Ocultar el mensaje después de 5 segundos\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        // Recargar la lista de documentos cuando se elimina uno\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_20__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_20__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>{\n                    setPlanEstudios(result);\n                },\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_20__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_20__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        // Convertir el plan estructurado a texto\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_20__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              // Convertir markdown a HTML b\\xe1sico para impresi\\xf3n\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const tabs = [\n        {\n            id: 'dashboard',\n            label: 'Principal',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiRefreshCw, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 50\n            }, this),\n            color: 'bg-gradient-to-r from-blue-600 to-purple-600'\n        },\n        {\n            id: 'temario',\n            label: 'Mi Temario',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 49\n            }, this),\n            color: 'bg-green-600'\n        },\n        {\n            id: 'planEstudios',\n            label: 'Mi Plan de Estudios',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiCalendar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 63\n            }, this),\n            color: 'bg-teal-600'\n        },\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiMessageSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiLayers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiFileText, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiCheckSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        },\n        {\n            id: 'gestionar',\n            label: 'Gestionar Documentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiSettings, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 61\n            }, this),\n            color: 'bg-gray-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xfa de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    ref: documentSelectorRef,\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onNavigateToTab: setActiveTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                                children: \"Mi Plan de Estudios\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleGenerarPlanEstudios,\n                                                                            disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                            className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiRefreshCw, {\n                                                                                    className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 421,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Regenerar Plan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleDescargarPlan,\n                                                                            className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiDownload, {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 428,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Descargar\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleImprimirPlan,\n                                                                            className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiPrinter, {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Imprimir\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                children: \"Generando tu plan personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 25\n                                                    }, this) : planEstudios ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanEstudiosViewer, {\n                                                        plan: planEstudios,\n                                                        temarioId: temarioId || ''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiCalendar, {\n                                                                    className: \"w-10 h-10 text-teal-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                children: \"Genera tu Plan de Estudios Personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleGenerarPlanEstudios,\n                                                                disabled: !tienePlanificacion,\n                                                                className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiCalendar, {\n                                                                        className: \"w-5 h-5 mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Generar Plan de Estudios\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-red-600 mt-4\",\n                                                                children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"tt7/fuPqc40125gJnIme7W7nT0k=\", true, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_16__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_17__.useBackgroundGeneration\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});