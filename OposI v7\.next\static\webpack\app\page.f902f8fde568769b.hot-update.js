"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiCheckSquare,FiChevronRight,FiDownload,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiPrinter,FiRefreshCw,FiSettings,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticoSupabase__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../features/shared/components/DiagnosticoSupabase */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticoSupabase.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TabButton = (param)=>{\n    let { active, onClick, icon, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 5\n            }, undefined),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined);\n};\n_c = TabButton;\nfunction Home() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Hooks para el sistema de tareas en segundo plano\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_18__.useBackgroundGeneration)();\n    // Hook para manejar los resultados del plan de estudios\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_19__.usePlanEstudiosResults)({\n        onResult: {\n            \"Home.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"Home.usePlanEstudiosResults\"],\n        onError: {\n            \"Home.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"Home.usePlanEstudiosResults\"]\n    });\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Cargar datos del temario y verificar planificación\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const cargarDatosTemario = {\n                \"Home.useEffect.cargarDatosTemario\": async ()=>{\n                    if (!user) return;\n                    try {\n                        const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_20__.obtenerTemarioUsuario)();\n                        if (temario) {\n                            setTemarioId(temario.id);\n                            const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_21__.tienePlanificacionConfigurada)(temario.id);\n                            setTienePlanificacion(tienePlan);\n                            // Verificar si ya existe un plan de estudios guardado\n                            const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_22__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                            if (planExistente && planExistente.plan_data) {\n                                console.log('✅ Plan de estudios existente encontrado');\n                                setPlanEstudios(planExistente.plan_data);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error al cargar datos del temario:', error);\n                    }\n                }\n            }[\"Home.useEffect.cargarDatosTemario\"];\n            cargarDatosTemario();\n        }\n    }[\"Home.useEffect\"], [\n        user\n    ]);\n    // Actualizar el plan cuando se reciba un resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        latestResult\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        // Recargar la lista de documentos automáticamente\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        // Ocultar el mensaje después de 5 segundos\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        // Recargar la lista de documentos cuando se elimina uno\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>{\n                    setPlanEstudios(result);\n                },\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        // Convertir el plan estructurado a texto\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_24__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              // Convertir markdown a HTML b\\xe1sico para impresi\\xf3n\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const tabs = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiRefreshCw, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 50\n            }, this),\n            color: 'bg-gradient-to-r from-blue-600 to-purple-600'\n        },\n        {\n            id: 'temario',\n            label: 'Mi Temario',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 49\n            }, this),\n            color: 'bg-green-600'\n        },\n        {\n            id: 'planEstudios',\n            label: 'Mi Plan de Estudios',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCalendar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 63\n            }, this),\n            color: 'bg-teal-600'\n        },\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiMessageSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiLayers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiFileText, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCheckSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        },\n        {\n            id: 'gestionar',\n            label: 'Gestionar Documentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiSettings, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 61\n            }, this),\n            color: 'bg-gray-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xfa de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    ref: documentSelectorRef,\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onNavigateToTab: setActiveTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                                children: \"Mi Plan de Estudios\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleGenerarPlanEstudios,\n                                                                            disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                            className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiRefreshCw, {\n                                                                                    className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 422,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Regenerar Plan\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleDescargarPlan,\n                                                                            className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiDownload, {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 429,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Descargar\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 425,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleImprimirPlan,\n                                                                            className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiPrinter, {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 436,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Imprimir\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 432,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                children: \"Generando tu plan personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 25\n                                                    }, this) : planEstudios ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        plan: planEstudios,\n                                                        temarioId: temarioId || ''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCalendar, {\n                                                                    className: \"w-10 h-10 text-teal-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                children: \"Genera tu Plan de Estudios Personalizado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleGenerarPlanEstudios,\n                                                                disabled: !tienePlanificacion,\n                                                                className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiDownload_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiPrinter_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiCalendar, {\n                                                                        className: \"w-5 h-5 mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Generar Plan de Estudios\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-red-600 mt-4\",\n                                                                children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'diagnostico' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticoSupabase__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 547,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"tt7/fuPqc40125gJnIme7W7nT0k=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_18__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_19__.usePlanEstudiosResults\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});