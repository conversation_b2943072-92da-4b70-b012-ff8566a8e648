"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/flashcardsService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/flashcardsService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarFlashcard: () => (/* binding */ actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* binding */ actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* binding */ crearColeccionFlashcards),\n/* harmony export */   crearFlashcard: () => (/* binding */ crearFlashcard),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* binding */ eliminarColeccionFlashcards),\n/* harmony export */   eliminarFlashcard: () => (/* binding */ eliminarFlashcard),\n/* harmony export */   guardarFlashcards: () => (/* binding */ guardarFlashcards),\n/* harmony export */   guardarRevisionHistorial: () => (/* binding */ guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* binding */ obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* binding */ obtenerColeccionesFlashcards),\n/* harmony export */   obtenerFlashcardPorId: () => (/* binding */ obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* binding */ obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* binding */ obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* binding */ obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* binding */ obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* binding */ obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* binding */ obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* binding */ obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* binding */ obtenerProgresoFlashcard),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* binding */ registrarRespuestaFlashcard),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* binding */ reiniciarProgresoFlashcard)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea una nueva colección de flashcards\n */ async function crearColeccionFlashcards(titulo, descripcion) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').insert([\n            {\n                titulo,\n                descripcion,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear colección de flashcards:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al crear colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las colecciones de flashcards del usuario actual\n */ async function obtenerColeccionesFlashcards() {\n    try {\n        // Obtener el usuario actual\n        const { user, error: userError } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (userError) {\n            console.error('Error al obtener usuario:', userError);\n            return [];\n        }\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener colecciones de flashcards:', error);\n            console.error('Detalles del error:', error);\n            return [];\n        }\n        if (!data || data.length === 0) {\n            return [];\n        }\n        // Obtener el conteo de flashcards y estadísticas básicas para cada colección\n        const coleccionesConConteo = await Promise.all(data.map(async (coleccion)=>{\n            try {\n                // Usar una consulta simple para contar flashcards\n                const { data: flashcardsData, error: flashcardsError } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccion.id);\n                if (flashcardsError) {\n                    console.error('Error al contar flashcards para colección', coleccion.id, ':', flashcardsError);\n                    return {\n                        ...coleccion,\n                        numero_flashcards: 0,\n                        pendientes_hoy: 0\n                    };\n                }\n                // Obtener flashcards pendientes para hoy\n                const { data: pendientesData, error: pendientesError } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select(\"\\n              id,\\n              progreso_flashcards!inner(\\n                proxima_revision,\\n                estado\\n              )\\n            \").eq('coleccion_id', coleccion.id).lte('progreso_flashcards.proxima_revision', new Date().toISOString());\n                const pendientesHoy = pendientesError ? 0 : (pendientesData === null || pendientesData === void 0 ? void 0 : pendientesData.length) || 0;\n                return {\n                    ...coleccion,\n                    numero_flashcards: (flashcardsData === null || flashcardsData === void 0 ? void 0 : flashcardsData.length) || 0,\n                    pendientes_hoy: pendientesHoy\n                };\n            } catch (error) {\n                console.error('Error al procesar colección', coleccion.id, ':', error);\n                return {\n                    ...coleccion,\n                    numero_flashcards: 0,\n                    pendientes_hoy: 0\n                };\n            }\n        }));\n        return coleccionesConConteo;\n    } catch (error) {\n        console.error('Error general al obtener colecciones de flashcards:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)\n */ async function obtenerColeccionFlashcardsPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener colección de flashcards:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva flashcard\n */ async function crearFlashcard(coleccionId, pregunta, respuesta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert([\n        {\n            coleccion_id: coleccionId,\n            pregunta,\n            respuesta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear flashcard:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las flashcards de una colección\n */ async function obtenerFlashcardsPorColeccionId(coleccionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener flashcards:', error);\n        return [];\n    }\n    return data || [];\n}\n// Alias para mantener compatibilidad con el código existente\nconst obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;\n/**\n * Guarda múltiples flashcards en la base de datos\n */ async function guardarFlashcards(flashcards) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert(flashcards).select();\n    if (error) {\n        console.error('Error al guardar flashcards:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((card)=>card.id)) || null;\n}\n/**\n * Obtiene una flashcard por su ID\n */ async function obtenerFlashcardPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener flashcard:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene el progreso de una flashcard\n */ async function obtenerProgresoFlashcard(flashcardId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').eq('flashcard_id', flashcardId).maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro\n    if (error) {\n        console.error('Error al obtener progreso de flashcard:', error);\n        return null;\n    }\n    return data || null;\n}\n/**\n * Obtiene todas las flashcards con su progreso para una colección\n */ async function obtenerFlashcardsParaEstudiar(coleccionId) {\n    // Obtener todas las flashcards de la colección\n    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);\n    // Obtener el progreso de todas las flashcards en una sola consulta\n    const { data: progresos, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((f)=>f.id));\n    if (error) {\n        console.error('Error al obtener progreso de flashcards:', error);\n        return [];\n    }\n    // Fecha actual para comparar\n    const ahora = new Date();\n    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n    // Combinar flashcards con su progreso\n    return flashcards.map((flashcard)=>{\n        const progreso = progresos === null || progresos === void 0 ? void 0 : progresos.find((p)=>p.flashcard_id === flashcard.id);\n        if (!progreso) {\n            // Si no hay progreso, es una tarjeta nueva que debe estudiarse\n            return {\n                ...flashcard,\n                debeEstudiar: true\n            };\n        }\n        // Determinar si la flashcard debe estudiarse hoy\n        const proximaRevision = new Date(progreso.proxima_revision);\n        const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n        const debeEstudiar = proximaRevisionSinHora <= hoy;\n        return {\n            ...flashcard,\n            debeEstudiar,\n            progreso: {\n                factor_facilidad: progreso.factor_facilidad,\n                intervalo: progreso.intervalo,\n                repeticiones: progreso.repeticiones,\n                estado: progreso.estado,\n                proxima_revision: progreso.proxima_revision\n            }\n        };\n    });\n}\n/**\n * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)\n */ async function obtenerFlashcardsMasDificiles(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        // Obtener todas las flashcards con progreso\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener historial de revisiones para calcular dificultad\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: historial, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, dificultad').in('flashcard_id', flashcardIds);\n        if (error) {\n            console.error('Error al obtener historial de revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Calcular estadísticas de dificultad por flashcard\n        const estadisticasDificultad = new Map();\n        historial === null || historial === void 0 ? void 0 : historial.forEach((revision)=>{\n            const stats = estadisticasDificultad.get(revision.flashcard_id) || {\n                dificil: 0,\n                total: 0\n            };\n            stats.total++;\n            if (revision.dificultad === 'dificil') {\n                stats.dificil++;\n            }\n            estadisticasDificultad.set(revision.flashcard_id, stats);\n        });\n        // Ordenar por dificultad (ratio de respuestas difíciles)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const stats = estadisticasDificultad.get(flashcard.id);\n            const ratioDificultad = stats ? stats.dificil / stats.total : 0;\n            return {\n                ...flashcard,\n                ratioDificultad\n            };\n        }).sort((a, b)=>b.ratioDificultad - a.ratioDificultad).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards más difíciles:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards aleatorias de una colección\n */ async function obtenerFlashcardsAleatorias(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Mezclar aleatoriamente y tomar el límite\n        const flashcardsMezcladas = [\n            ...flashcardsConProgreso\n        ].sort(()=>Math.random() - 0.5).slice(0, limite);\n        return flashcardsMezcladas;\n    } catch (error) {\n        console.error('Error al obtener flashcards aleatorias:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards que no se han estudiado recientemente\n */ async function obtenerFlashcardsNoRecientes(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener última revisión de cada flashcard\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: ultimasRevisiones, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, fecha').in('flashcard_id', flashcardIds).order('fecha', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener últimas revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Obtener la fecha más reciente por flashcard\n        const ultimaRevisionPorFlashcard = new Map();\n        ultimasRevisiones === null || ultimasRevisiones === void 0 ? void 0 : ultimasRevisiones.forEach((revision)=>{\n            if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {\n                ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha);\n            }\n        });\n        // Ordenar por fecha de última revisión (más antiguas primero)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const ultimaRevision = ultimaRevisionPorFlashcard.get(flashcard.id);\n            return {\n                ...flashcard,\n                ultimaRevision: ultimaRevision ? new Date(ultimaRevision) : new Date(0)\n            };\n        }).sort((a, b)=>a.ultimaRevision.getTime() - b.ultimaRevision.getTime()).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards no recientes:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards por estado específico\n */ async function obtenerFlashcardsPorEstado(coleccionId, estado) {\n    let limite = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Filtrar por estado y limitar\n        const flashcardsFiltradas = flashcardsConProgreso.filter((flashcard)=>{\n            if (!flashcard.progreso) {\n                return estado === 'nuevo';\n            }\n            return flashcard.progreso.estado === estado;\n        }).slice(0, limite);\n        return flashcardsFiltradas;\n    } catch (error) {\n        console.error('Error al obtener flashcards por estado:', error);\n        return [];\n    }\n}\n/**\n * Registra una respuesta a una flashcard y actualiza su progreso\n * Versión mejorada para evitar errores 409\n */ async function registrarRespuestaFlashcard(flashcardId, dificultad) {\n    try {\n        // Primero intentamos obtener el progreso existente\n        let factorFacilidad = 2.5;\n        let intervalo = 1;\n        let repeticiones = 0;\n        let estado = 'nuevo';\n        let progresoExiste = false;\n        // Intentar obtener progreso existente\n        const { data: progresoExistente, error: errorConsulta } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('factor_facilidad, intervalo, repeticiones, estado').eq('flashcard_id', flashcardId).single();\n        if (!errorConsulta && progresoExistente) {\n            factorFacilidad = progresoExistente.factor_facilidad || 2.5;\n            intervalo = progresoExistente.intervalo || 1;\n            repeticiones = progresoExistente.repeticiones || 0;\n            estado = progresoExistente.estado || 'nuevo';\n            progresoExiste = true;\n        }\n        // Aplicar el algoritmo SM-2 para calcular el nuevo progreso\n        let nuevoFactorFacilidad = factorFacilidad;\n        let nuevoIntervalo = intervalo;\n        let nuevasRepeticiones = repeticiones;\n        let nuevoEstado = estado;\n        // Ajustar el factor de facilidad según la dificultad reportada\n        if (dificultad === 'dificil') {\n            nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);\n            nuevasRepeticiones = 0;\n            nuevoIntervalo = 1;\n            nuevoEstado = 'aprendiendo';\n        } else {\n            nuevasRepeticiones++;\n            if (dificultad === 'normal') {\n                nuevoFactorFacilidad = factorFacilidad - 0.15;\n            } else if (dificultad === 'facil') {\n                nuevoFactorFacilidad = factorFacilidad + 0.1;\n            }\n            nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));\n            // Calcular el nuevo intervalo\n            if (nuevasRepeticiones === 1) {\n                nuevoIntervalo = 1;\n                nuevoEstado = 'aprendiendo';\n            } else if (nuevasRepeticiones === 2) {\n                nuevoIntervalo = 6;\n                nuevoEstado = 'repasando';\n            } else {\n                nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);\n                nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';\n            }\n        }\n        // Calcular la próxima fecha de revisión\n        const ahora = new Date();\n        const proximaRevision = new Date(ahora);\n        proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);\n        // Guardar el nuevo progreso usando insert o update según corresponda\n        let errorProgreso = null;\n        if (progresoExiste) {\n            // Actualizar progreso existente\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            }).eq('flashcard_id', flashcardId);\n            errorProgreso = error;\n        } else {\n            // Crear nuevo progreso\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').insert({\n                flashcard_id: flashcardId,\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            });\n            errorProgreso = error;\n        }\n        if (errorProgreso) {\n            console.error('Error al guardar progreso:', errorProgreso);\n            return false;\n        }\n        // Guardar en el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert({\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: nuevoFactorFacilidad,\n            intervalo: nuevoIntervalo,\n            repeticiones: nuevasRepeticiones,\n            fecha: ahora.toISOString()\n        });\n        if (errorHistorial) {\n        // No retornamos false aquí porque el progreso ya se guardó correctamente\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n// Alias para mantener compatibilidad con el código existente\nconst actualizarProgresoFlashcard = registrarRespuestaFlashcard;\n/**\n * Guarda una revisión en el historial\n */ async function guardarRevisionHistorial(flashcardId, dificultad, factorFacilidad, intervalo, repeticiones) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert([\n        {\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: factorFacilidad,\n            intervalo,\n            repeticiones\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al guardar revisión en historial:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Reinicia el progreso de una flashcard\n */ async function reiniciarProgresoFlashcard(flashcardId) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n        factor_facilidad: 2.5,\n        intervalo: 0,\n        repeticiones: 0,\n        estado: 'nuevo',\n        ultima_revision: new Date().toISOString(),\n        proxima_revision: new Date().toISOString()\n    }).eq('flashcard_id', flashcardId);\n    if (error) {\n        console.error('Error al reiniciar progreso de flashcard:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Actualiza una flashcard existente\n */ async function actualizarFlashcard(flashcardId, pregunta, respuesta) {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').update({\n            pregunta,\n            respuesta,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', flashcardId);\n        if (error) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una flashcard y todo su progreso asociado\n */ async function eliminarFlashcard(flashcardId) {\n    try {\n        // Primero eliminar el progreso asociado\n        const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().eq('flashcard_id', flashcardId);\n        if (errorProgreso) {\n            return false;\n        }\n        // Eliminar el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().eq('flashcard_id', flashcardId);\n        if (errorHistorial) {\n            return false;\n        }\n        // Finalmente eliminar la flashcard\n        const { error: errorFlashcard, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete({\n            count: 'exact'\n        }).eq('id', flashcardId);\n        if (errorFlashcard) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una colección completa de flashcards y todo su contenido asociado\n */ async function eliminarColeccionFlashcards(coleccionId) {\n    try {\n        // Obtener el usuario actual para verificar permisos\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return false;\n        }\n        // Primero obtener todas las flashcards de la colección\n        const { data: flashcards, error: errorFlashcards } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccionId);\n        if (errorFlashcards) {\n            return false;\n        }\n        const flashcardIds = (flashcards === null || flashcards === void 0 ? void 0 : flashcards.map((fc)=>fc.id)) || [];\n        // Eliminar progreso de todas las flashcards\n        if (flashcardIds.length > 0) {\n            const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().in('flashcard_id', flashcardIds);\n            if (errorProgreso) {\n                return false;\n            }\n            // Eliminar historial de todas las flashcards\n            const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().in('flashcard_id', flashcardIds);\n            if (errorHistorial) {\n                return false;\n            }\n            // Eliminar todas las flashcards de la colección\n            const { error: errorFlashcardsDelete } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete().eq('coleccion_id', coleccionId);\n            if (errorFlashcardsDelete) {\n                return false;\n            }\n        }\n        // Finalmente eliminar la colección\n        const { error: errorColeccion, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').delete({\n            count: 'exact'\n        }).eq('id', coleccionId).eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual\n        if (errorColeccion) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\n"));

/***/ })

});