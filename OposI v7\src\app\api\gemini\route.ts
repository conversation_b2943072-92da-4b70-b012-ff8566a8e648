import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { generarTest, generarFlashcards, generarMapaMental } from '@/lib/gemini';
import { obtenerRespuestaIA } from '@/lib/gemini/questionService';
import { generarPlanEstudios } from '@/features/planificacion/services/planGeneratorService';
import { ApiGeminiInputSchema } from '@/lib/zodSchemas';

// API route for Gemini actions
export async function POST(req: NextRequest) {
  try {
    console.log('🔍 === INICIO API GEMINI ===');
    console.log('📥 Headers:', Object.fromEntries(req.headers.entries()));

    // Crear cliente de Supabase usando la implementación correcta
    const supabase = await createServerSupabaseClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Diagnóstico mejorado para debugging
    console.log('User check:', {
      hasUser: !!user,
      userError: userError?.message,
      userId: user?.id,
      cookies: req.headers.get('cookie')?.includes('supabase') ? 'present' : 'missing'
    });

    if (!user) {
      console.log('❌ Usuario no autenticado');
      return NextResponse.json({
        error: 'Unauthorized',
        debug: {
          userError: userError?.message,
          hasCookies: !!req.headers.get('cookie')
        }
      }, { status: 401 });
    }

    const body = await req.json();
    console.log('📥 Body recibido:', JSON.stringify(body, null, 2));

    // Validación robusta de entrada
    const parseResult = ApiGeminiInputSchema.safeParse(body);
    if (!parseResult.success) {
      console.log('❌ Error de validación:', parseResult.error.errors);
      return NextResponse.json({
        error: 'Datos inválidos',
        detalles: parseResult.error.errors
      }, { status: 400 });
    }
    console.log('✅ Validación exitosa');

    // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA
    if (body.pregunta && body.documentos) {
      console.log('🔄 Procesando pregunta+documentos');
      const result = await obtenerRespuestaIA(body.pregunta, body.documentos);
      return NextResponse.json({ result });
    }

    const { action, peticion, contextos, temarioId } = body;
    console.log('🎯 Acción a ejecutar:', action);
    console.log('📋 Petición:', peticion);
    console.log('📚 Contextos:', contextos?.length || 0, 'elementos');
    console.log('🆔 TemarioId:', temarioId);

    let result;
    switch (action) {
      case 'generarTest':
        console.log('🧪 Generando test...');
        result = await generarTest(peticion, contextos);
        break;
      case 'generarFlashcards':
        console.log('🃏 Generando flashcards...');
        result = await generarFlashcards(peticion, contextos);
        break;
      case 'generarMapaMental':
        console.log('🗺️ Generando mapa mental...');
        result = await generarMapaMental(peticion, contextos);
        break;
      case 'generarPlanEstudios':
        console.log('📅 Generando plan de estudios...');
        // Para planes de estudios, el temarioId viene en peticion
        const temarioIdFromPeticion = peticion || temarioId;
        console.log('🆔 TemarioId final:', temarioIdFromPeticion);
        if (!temarioIdFromPeticion) {
          console.log('❌ Error: No se proporcionó temarioId');
          throw new Error('Se requiere temarioId para generar el plan de estudios');
        }
        result = await generarPlanEstudios(temarioIdFromPeticion, user);
        break;
      default:
        console.log('❌ Acción no soportada:', action);
        return NextResponse.json({ error: 'Acción no soportada' }, { status: 400 });
    }

    console.log('✅ Resultado generado exitosamente');
    console.log('📤 Enviando respuesta...');
    return NextResponse.json({ result });
  } catch (error) {
    console.log('💥 Error al iniciar generación del plan:', error);
    console.error('Error al generar plan de estudios:', error);
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
}
