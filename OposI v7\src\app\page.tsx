'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { FiBook, FiMessageSquare, FiLayers, FiFileText, FiUpload, FiChevronRight, FiCheck, FiList, FiCheckSquare, FiLogOut, FiRefreshCw, FiSettings, FiCalendar } from 'react-icons/fi';
import DocumentSelector, { DocumentSelectorRef } from '../features/documents/components/DocumentSelector';
import QuestionForm from '../features/conversations/components/QuestionForm';
import DocumentUploader from '../features/documents/components/DocumentUploader';
import MindMapGenerator from '../features/mindmaps/components/MindMapGenerator';
import FlashcardGenerator from '../features/flashcards/components/FlashcardGenerator';
import DocumentManager from '../features/documents/components/DocumentManager';
import FlashcardViewer from '../features/flashcards/components/FlashcardViewer';
import TestGenerator from '../features/tests/components/TestGenerator';
import TestViewer from '../features/tests/components/TestViewer';
import Dashboard from '../features/dashboard/components/Dashboard';
import TemarioManager from '../features/temario/components/TemarioManager';
import MobileDebugInfo from '../features/shared/components/MobileDebugInfo';
import DiagnosticoSupabase from '../features/shared/components/DiagnosticoSupabase';
import DiagnosticPanel from '../features/shared/components/DiagnosticPanel';
import { Documento } from '../lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { useBackgroundGeneration } from '@/hooks/useBackgroundGeneration';
import { usePlanEstudiosResults } from '@/hooks/usePlanEstudiosResults';
import { obtenerTemarioUsuario } from '@/features/temario/services/temarioService';
import { tienePlanificacionConfigurada } from '@/features/planificacion/services/planificacionService';
import { PlanEstudiosEstructurado } from '@/features/planificacion/services/planGeneratorService';
// import PlanEstudiosViewer from '@/features/planificacion/components/PlanEstudiosViewer';
import { toast } from 'react-hot-toast';

type TabType = 'dashboard' | 'preguntas' | 'mapas' | 'flashcards' | 'misFlashcards' | 'tests' | 'misTests' | 'temario' | 'planEstudios' | 'gestionar' | 'diagnostico';

interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
  color: string;
}

const TabButton: React.FC<TabButtonProps> = ({ active, onClick, icon, label, color }) => (
  <button
    onClick={onClick}
    className={`flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm ${
      active
        ? `text-white ${color} shadow-md`
        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
    }`}
  >
    <span className="mr-2">{icon}</span>
    {label}
    {active && <FiChevronRight className="ml-2" />}
  </button>
);

export default function Home() {
  const [documentosSeleccionados, setDocumentosSeleccionados] = useState<Documento[]>([]);
  const [mostrarUploader, setMostrarUploader] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);
  const [isRefreshingDocuments, setIsRefreshingDocuments] = useState(false);
  const [planEstudios, setPlanEstudios] = useState<PlanEstudiosEstructurado | null>(null);
  const [temarioId, setTemarioId] = useState<string | null>(null);
  const [tienePlanificacion, setTienePlanificacion] = useState<boolean>(false);
  const { cerrarSesion, user, isLoading } = useAuth();
  const router = useRouter();
  const documentSelectorRef = useRef<DocumentSelectorRef>(null);

  // Hooks para el sistema de tareas en segundo plano
  const { generatePlanEstudios, isGenerating } = useBackgroundGeneration();

  // Hook para manejar los resultados del plan de estudios
  const { latestResult, isLoading: isPlanLoading } = usePlanEstudiosResults({
    onResult: (result) => {
      setPlanEstudios(result);
      toast.success('¡Plan de estudios generado exitosamente!');
    },
    onError: (error) => {
      toast.error(`Error al generar plan: ${error}`);
    }
  });

  // No necesitamos verificar autenticación aquí, el middleware ya lo hace
  useEffect(() => {
    console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);
  }, [user, isLoading]);

  // Cargar datos del temario y verificar planificación
  useEffect(() => {
    const cargarDatosTemario = async () => {
      if (!user) return;

      try {
        const temario = await obtenerTemarioUsuario();
        if (temario) {
          setTemarioId(temario.id);
          const tienePlan = await tienePlanificacionConfigurada(temario.id);
          setTienePlanificacion(tienePlan);
        }
      } catch (error) {
        console.error('Error al cargar datos del temario:', error);
      }
    };

    cargarDatosTemario();
  }, [user]);

  // Actualizar el plan cuando se reciba un resultado
  useEffect(() => {
    if (latestResult) {
      setPlanEstudios(latestResult);
    }
  }, [latestResult]);

  // Si está cargando o no hay usuario, mostrar pantalla de carga
  if (isLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  const handleUploadSuccess = async () => {
    setShowUploadSuccess(true);
    setIsRefreshingDocuments(true);

    // Recargar la lista de documentos automáticamente
    try {
      await documentSelectorRef.current?.recargarDocumentos();
    } catch (error) {
      console.error('Error al recargar documentos:', error);
    } finally {
      setIsRefreshingDocuments(false);
    }

    // Ocultar el mensaje después de 5 segundos
    setTimeout(() => setShowUploadSuccess(false), 5000);
  };

  const handleDocumentDeleted = async () => {
    // Recargar la lista de documentos cuando se elimina uno
    try {
      await documentSelectorRef.current?.recargarDocumentos();
    } catch (error) {
      console.error('Error al recargar documentos después de eliminar:', error);
    }
  };

  const handleLogout = async () => {
    await cerrarSesion();
  };

  const handleGenerarPlanEstudios = async () => {
    if (!temarioId) {
      toast.error('No se encontró un temario configurado');
      return;
    }

    if (!tienePlanificacion) {
      toast.error('Necesitas completar la configuración de planificación en "Mi Temario"');
      return;
    }

    try {
      await generatePlanEstudios({
        temarioId,
        onComplete: (result) => {
          setPlanEstudios(result);
        },
        onError: (error) => {
          if (error.includes('planificación configurada')) {
            toast.error('Necesitas completar la configuración de planificación en "Mi Temario"');
          } else {
            toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');
          }
        }
      });
    } catch (error) {
      console.error('Error al iniciar generación del plan:', error);
    }
  };

  const tabs: { id: TabType; label: string; icon: React.ReactNode; color: string }[] = [
    { id: 'dashboard', label: 'Dashboard', icon: <FiRefreshCw />, color: 'bg-gradient-to-r from-blue-600 to-purple-600' },
    { id: 'temario', label: 'Mi Temario', icon: <FiBook />, color: 'bg-green-600' },
    { id: 'planEstudios', label: 'Mi Plan de Estudios', icon: <FiCalendar />, color: 'bg-teal-600' },
    { id: 'preguntas', label: 'Preguntas y Respuestas', icon: <FiMessageSquare />, color: 'bg-blue-600' },
    { id: 'mapas', label: 'Mapas Mentales', icon: <FiLayers />, color: 'bg-purple-600' },
    { id: 'flashcards', label: 'Generar Flashcards', icon: <FiFileText />, color: 'bg-orange-500' },
    { id: 'tests', label: 'Generar Tests', icon: <FiList />, color: 'bg-indigo-600' },
    { id: 'misFlashcards', label: 'Mis Flashcards', icon: <FiBook />, color: 'bg-emerald-600' },
    { id: 'misTests', label: 'Mis Tests', icon: <FiCheckSquare />, color: 'bg-pink-600' },
    { id: 'gestionar', label: 'Gestionar Documentos', icon: <FiSettings />, color: 'bg-gray-600' },
    // { id: 'diagnostico', label: 'Diagnóstico DB', icon: <FiSettings />, color: 'bg-red-600' }, // Oculto pero disponible para futuro uso
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                OposiAI
              </h1>
              <p className="text-sm text-gray-500">Tu asistente inteligente para oposiciones</p>
            </div>
            <div className="flex items-center space-x-4">
              {user && (
                <div className="text-sm text-gray-600">
                  Hola, {user.email?.split('@')[0]}
                </div>
              )}
              <button
                onClick={() => setMostrarUploader(!mostrarUploader)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                <FiUpload className="mr-2" />
                {mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'}
              </button>
              <button
                onClick={handleLogout}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiLogOut className="mr-2" />
                Cerrar sesión
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="px-4 sm:px-6 lg:px-8 py-8">
        {/* Uploader */}
        {mostrarUploader && (
          <div className="mb-8 transition-all duration-300 ease-in-out">
            <DocumentUploader onSuccess={handleUploadSuccess} />
          </div>
        )}

        {showUploadSuccess && (
          <div className="mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200">
            <div className="flex items-center">
              <FiCheck className="text-green-500 mr-2 flex-shrink-0" />
              <div>
                <p className="font-medium">¡Documento subido exitosamente!</p>
                <p className="text-sm text-green-700 mt-1">
                  {isRefreshingDocuments ? (
                    <span className="flex items-center">
                      <FiRefreshCw className="animate-spin mr-1" />
                      Actualizando lista de documentos...
                    </span>
                  ) : (
                    "El documento ya está disponible en los desplegables de selección."
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Layout expandido para todas las pestañas - ancho completo */}
        <div className="flex gap-6 mb-8">
          {/* Sidebar compacto */}
          <div className="w-80 flex-shrink-0">
            <div className="bg-white rounded-xl shadow-sm p-4 sticky top-6">
              <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2">
                Menú de Estudio
              </h2>
              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <TabButton
                    key={tab.id}
                    active={activeTab === tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    icon={tab.icon}
                    label={tab.label}
                    color={tab.color}
                  />
                ))}
              </nav>

              <div className="mt-8 pt-6 border-t border-gray-100">
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2">
                  Documentos Seleccionados
                </h3>
                <DocumentSelector
                  ref={documentSelectorRef}
                  onSelectionChange={setDocumentosSeleccionados}
                />
              </div>
            </div>
          </div>

          {/* Área de contenido expandida */}
          <div className="flex-1">
            {activeTab === 'dashboard' ? (
              <Dashboard onNavigateToTab={setActiveTab} />
            ) : (
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="p-6">
                  {activeTab === 'temario' && <TemarioManager />}

                  {activeTab === 'planEstudios' && (
                    <div>
                      {/* Header */}
                      <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-semibold text-gray-900">Mi Plan de Estudios</h2>
                        <div className="flex gap-2">
                          {planEstudios && (
                            <button
                              onClick={handleGenerarPlanEstudios}
                              disabled={isPlanLoading || isGenerating('plan-estudios')}
                              className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                              <FiRefreshCw className={`w-4 h-4 ${(isPlanLoading || isGenerating('plan-estudios')) ? 'animate-spin' : ''}`} />
                              Regenerar Plan
                            </button>
                          )}
                        </div>
                      </div>

                      {/* Contenido */}
                      {isPlanLoading || isGenerating('plan-estudios') ? (
                        <div className="text-center py-12">
                          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
                          <h3 className="text-lg font-medium text-gray-900 mb-2">Generando tu plan personalizado</h3>
                          <p className="text-gray-600">La IA está analizando tu temario y configuración...</p>
                        </div>
                      ) : planEstudios ? (
                        <PlanEstudiosViewer
                          plan={planEstudios}
                          temarioId={temarioId || ''}
                        />
                      ) : (
                        <div className="text-center py-12">
                          <div className="w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <FiCalendar className="w-10 h-10 text-teal-600" />
                          </div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">
                            Genera tu Plan de Estudios Personalizado
                          </h3>
                          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                            Crea un plan de estudios personalizado basado en tu temario y configuración de planificación
                          </p>
                          <button
                            onClick={handleGenerarPlanEstudios}
                            disabled={!tienePlanificacion}
                            className="inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            <FiCalendar className="w-5 h-5 mr-3" />
                            Generar Plan de Estudios
                          </button>
                          {!tienePlanificacion && (
                            <p className="text-sm text-red-600 mt-4">
                              Necesitas completar la configuración de planificación en "Mi Temario"
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === 'preguntas' && (
                    <QuestionForm documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'mapas' && (
                    <MindMapGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'flashcards' && (
                    <FlashcardGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'tests' && (
                    <TestGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'misTests' && <TestViewer />}

                  {activeTab === 'misFlashcards' && <FlashcardViewer />}

                  {activeTab === 'gestionar' && (
                    <DocumentManager onDocumentDeleted={handleDocumentDeleted} />
                  )}

                  {activeTab === 'diagnostico' && <DiagnosticoSupabase />}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center">
              <span className="text-gray-500 text-sm">
                &copy; {new Date().getFullYear()} OposiAI - Asistente para Oposiciones
              </span>
            </div>
            <div className="mt-4 md:mt-0">
              <nav className="flex space-x-6">
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Términos
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Privacidad
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Contacto
                </a>
              </nav>
            </div>
          </div>
        </div>
      </footer>

      {/* Componente de debug para dispositivos móviles */}
      <MobileDebugInfo />

      {/* Panel de diagnóstico */}
      <DiagnosticPanel />
    </div>
  );
}
