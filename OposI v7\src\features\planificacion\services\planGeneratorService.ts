import { model } from '@/lib/gemini/geminiClient';
import { obtenerEstimacionesTemas } from './planificacionService';
import { PROMPT_PLAN_ESTUDIOS } from '@/config/prompts';
import { Tema, PlanificacionUsuario } from '@/lib/supabase/supabaseClient';
import { User } from '@supabase/supabase-js';

// Tipos para el plan estructurado
export interface TareaPlan {
  titulo: string;
  descripcion: string;
  tipo: 'estudio' | 'repaso' | 'practica' | 'evaluacion';
  duracionEstimada: string;
}

export interface DiaPlan {
  dia: string;
  horas: number;
  tareas: TareaPlan[];
}

export interface SemanaPlan {
  numero: number;
  fechaInicio: string;
  fechaFin: string;
  objetivoPrincipal: string;
  dias: DiaPlan[];
}

export interface ResumenPlan {
  tiempoTotalEstudio: string;
  numeroTemas: number;
  duracionEstudioNuevo: string;
  duracionRepasoFinal: string;
  [key: string]: any; // Permitir propiedades adicionales
}

export interface PlanEstudiosEstructurado {
  introduccion: string;
  resumen: ResumenPlan;
  semanas: SemanaPlan[];
  estrategiaRepasos: string | { titulo?: string; descripcion?: string };
  proximosPasos: string | { titulo?: string; descripcion?: string };
}

interface DatosPlanificacion {
  tiempo_diario_promedio?: number;
  tiempo_por_dia?: Record<string, number>;
  fecha_examen?: string;
  fecha_examen_aproximada?: string;
  familiaridad_general?: number;
  preferencias_horario?: string[];
  frecuencia_repasos?: string;
}

interface DatosTema {
  id: string;
  numero: number;
  titulo: string;
  descripcion?: string;
  horasEstimadas?: number;
  esDificil: boolean;
  esMuyImportante: boolean;
  yaDominado: boolean;
  notas?: string;
}

/**
 * Obtiene la planificación del usuario desde el servidor
 */
async function obtenerPlanificacionUsuarioServidor(temarioId: string, userId: string): Promise<PlanificacionUsuario | null> {
  try {
    const { createServerSupabaseClient } = await import('@/lib/supabase/server');
    const supabase = await createServerSupabaseClient();

    console.log('🔍 Consultando planificación con:', { userId, temarioId });

    const { data, error } = await supabase
      .from('planificacion_usuario')
      .select('*')
      .eq('user_id', userId)
      .eq('temario_id', temarioId)
      .single();

    console.log('🔍 Resultado consulta planificación:', {
      data: data ? 'ENCONTRADA' : 'NO ENCONTRADA',
      error: error?.code,
      errorMessage: error?.message
    });

    if (error) {
      if (error.code === 'PGRST116') {
        console.log('❌ No hay planificación configurada (PGRST116)');
        return null;
      }
      console.error('❌ Error al obtener planificación:', error);
      return null;
    }

    console.log('✅ Planificación encontrada:', data.id);
    return data;
  } catch (error) {
    console.error('❌ Error al obtener planificación:', error);
    return null;
  }
}

/**
 * Obtiene los temas del temario desde el servidor
 */
async function obtenerTemasServidor(temarioId: string): Promise<Tema[]> {
  try {
    const { createServerSupabaseClient } = await import('@/lib/supabase/server');
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('temas')
      .select('*')
      .eq('temario_id', temarioId)
      .order('orden', { ascending: true });

    if (error) {
      console.error('Error al obtener temas:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener temas:', error);
    return [];
  }
}



/**
 * Genera un plan de estudios personalizado usando IA
 */
export async function generarPlanEstudios(temarioId: string, user?: User): Promise<PlanEstudiosEstructurado> {
  try {
    // Si no se proporciona usuario, obtenerlo (para compatibilidad con llamadas desde el servidor)
    let usuarioActual = user;
    if (!usuarioActual) {
      const { createServerSupabaseClient } = await import('@/lib/supabase/server');
      const supabase = await createServerSupabaseClient();
      const { data: { user: serverUser }, error: authError } = await supabase.auth.getUser();

      if (!serverUser || authError) {
        throw new Error('No hay usuario autenticado para generar el plan de estudios');
      }
      usuarioActual = serverUser;
    }

    // Obtener datos de planificación del usuario
    const planificacion = await obtenerPlanificacionUsuarioServidor(temarioId, usuarioActual.id);

    if (!planificacion) {
      throw new Error(`No se encontró planificación configurada para el temario: ${temarioId}`);
    }

    // Obtener estimaciones de temas
    const estimaciones = await obtenerEstimacionesTemas(planificacion.id);

    // Obtener temas del temario
    const temas = await obtenerTemasServidor(temarioId);

    // Combinar datos de temas con estimaciones
    const temasConEstimaciones: DatosTema[] = temas.map(tema => {
      const estimacion = estimaciones.find(est => est.tema_id === tema.id);
      return {
        id: tema.id,
        numero: tema.numero,
        titulo: tema.titulo,
        descripcion: tema.descripcion,
        horasEstimadas: estimacion?.horas_estimadas || 0,
        esDificil: estimacion?.es_dificil || false,
        esMuyImportante: estimacion?.es_muy_importante || false,
        yaDominado: estimacion?.ya_dominado || false,
        notas: estimacion?.notas || ''
      };
    });

    // Preparar información del usuario para el prompt
    const informacionUsuario = prepararInformacionUsuario(planificacion, temasConEstimaciones);

    // Construir el prompt final
    const promptFinal = PROMPT_PLAN_ESTUDIOS.replace('{informacionUsuario}', informacionUsuario);

    // Generar el plan con Gemini
    const result = await model.generateContent(promptFinal);
    const response = result.response.text();

    if (!response || response.trim().length === 0) {
      throw new Error('La IA no generó ningún contenido para el plan de estudios');
    }

    // Parsear el JSON generado por la IA
    try {
      // Intentar limpiar la respuesta si tiene texto adicional
      let jsonString = response.trim();

      // Si la respuesta contiene markdown, extraer solo el JSON
      if (jsonString.includes('```json')) {
        const jsonMatch = jsonString.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonString = jsonMatch[1].trim();
        }
      } else if (jsonString.includes('```')) {
        // Si tiene markdown sin especificar json
        const jsonMatch = jsonString.match(/```\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonString = jsonMatch[1].trim();
        }
      }

      // Si no empieza con {, buscar el primer {
      const startIndex = jsonString.indexOf('{');
      const endIndex = jsonString.lastIndexOf('}');

      if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
        jsonString = jsonString.substring(startIndex, endIndex + 1);
      }

      // Limpiar caracteres problemáticos básicos
      jsonString = jsonString
        .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Eliminar caracteres de control
        .replace(/\n\s*\n/g, '\n') // Eliminar líneas vacías múltiples
        .trim();

      const planEstructurado: PlanEstudiosEstructurado = JSON.parse(jsonString);

      // Guardar el plan en la base de datos
      const { guardarPlanEstudiosServidor } = await import('./planEstudiosService');
      const planId = await guardarPlanEstudiosServidor(
        temarioId,
        planEstructurado,
        usuarioActual,
        `Plan de Estudios - ${new Date().toLocaleDateString()}`
      );

      if (planId) {
        console.log('✅ Plan de estudios guardado con ID:', planId);
      } else {
        console.warn('⚠️ No se pudo guardar el plan en la base de datos');
      }

      return planEstructurado;
    } catch (parseError) {
      console.error('Error al parsear JSON del plan:', parseError);
      console.error('Respuesta de IA:', response);

      // Fallback: crear un plan básico con el texto recibido
      const planFallback: PlanEstudiosEstructurado = {
        introduccion: "Plan de estudios generado por IA",
        resumen: {
          tiempoTotalEstudio: "Por determinar",
          numeroTemas: temas.length,
          duracionEstudioNuevo: "Por determinar",
          duracionRepasoFinal: "Por determinar"
        },
        semanas: [{
          numero: 1,
          fechaInicio: new Date().toISOString().split('T')[0],
          fechaFin: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 6 días después (domingo de la misma semana)
          objetivoPrincipal: "Comenzar el estudio del temario",
          dias: [{
            dia: "Lunes",
            horas: 4,
            tareas: [{
              titulo: "Revisar plan detallado",
              descripcion: response.substring(0, 500) + "...",
              tipo: "estudio",
              duracionEstimada: "4h"
            }]
          }]
        }],
        estrategiaRepasos: "Revisar el contenido generado por la IA para más detalles",
        proximosPasos: "Consultar el plan completo y adaptarlo según necesidades"
      };

      // Guardar el plan fallback también
      const { guardarPlanEstudiosServidor } = await import('./planEstudiosService');
      const planId = await guardarPlanEstudiosServidor(
        temarioId,
        planFallback,
        usuarioActual,
        `Plan de Estudios (Fallback) - ${new Date().toLocaleDateString()}`
      );

      if (planId) {
        console.log('✅ Plan fallback guardado con ID:', planId);
      }

      return planFallback;
    }
  } catch (error) {
    console.error('Error al generar plan de estudios:', error);
    throw error;
  }
}

/**
 * Prepara la información del usuario en formato legible para la IA
 */
function prepararInformacionUsuario(
  planificacion: DatosPlanificacion,
  temas: DatosTema[]
): string {
  let info = '';

  // Fecha actual para el plan
  const fechaActual = new Date();
  const fechaInicioFormatted = fechaActual.toISOString().split('T')[0];

  info += `**FECHA ACTUAL:** ${fechaInicioFormatted}\n`;
  info += `**IMPORTANTE:** Todas las fechas del plan deben ser calculadas a partir de la fecha actual (${fechaInicioFormatted}).\n`;
  info += `- La Semana 1 debe comenzar el ${fechaInicioFormatted}\n`;
  info += `- Cada semana siguiente debe calcularse sumando 7 días a la anterior\n`;
  info += `- Usa el formato YYYY-MM-DD para todas las fechas\n\n`;

  // Disponibilidad de tiempo
  if (planificacion.tiempo_por_dia && Object.keys(planificacion.tiempo_por_dia).length > 0) {
    info += '**Disponibilidad de Tiempo Diario:**\n';
    const dias = ['lunes', 'martes', 'miercoles', 'jueves', 'viernes', 'sabado', 'domingo'];
    dias.forEach(dia => {
      const horas = planificacion.tiempo_por_dia![dia];
      if (horas) {
        info += `- ${dia.charAt(0).toUpperCase() + dia.slice(1)}: ${horas}h\n`;
      }
    });
  } else if (planificacion.tiempo_diario_promedio) {
    info += `**Disponibilidad de Tiempo Diario:** Promedio ${planificacion.tiempo_diario_promedio}h/día\n`;
  }

  // Fecha del examen
  if (planificacion.fecha_examen) {
    info += `\n**Fecha del Examen:** ${planificacion.fecha_examen}\n`;
  } else if (planificacion.fecha_examen_aproximada) {
    info += `\n**Fecha del Examen (aproximada):** ${planificacion.fecha_examen_aproximada}\n`;
  }

  // Familiaridad general
  if (planificacion.familiaridad_general) {
    info += `\n**Familiaridad General con el Temario (1-5):** ${planificacion.familiaridad_general}\n`;
  }

  // Preferencias de horario
  if (planificacion.preferencias_horario && planificacion.preferencias_horario.length > 0) {
    info += `\n**Preferencias de Horario:** ${planificacion.preferencias_horario.join(', ')}\n`;
  }

  // Frecuencia de repasos
  if (planificacion.frecuencia_repasos) {
    info += `\n**Frecuencia de Repasos Deseada:** ${planificacion.frecuencia_repasos}\n`;
  }

  // Índice del temario
  info += '\n**Índice del Temario del Opositor:**\n';
  temas.forEach(tema => {
    const caracteristicas = [];
    if (tema.esDificil) caracteristicas.push('dificil');
    if (tema.esMuyImportante) caracteristicas.push('muy_importante');
    if (tema.yaDominado) caracteristicas.push('ya_dominado');

    info += `- **Tema ${tema.numero}: ${tema.titulo}**\n`;
    if (tema.descripcion) {
      info += `  - Descripción: ${tema.descripcion}\n`;
    }
    if (tema.horasEstimadas && tema.horasEstimadas > 0) {
      info += `  - Estimación de horas: ${tema.horasEstimadas}h\n`;
    }
    if (caracteristicas.length > 0) {
      info += `  - Características: ${caracteristicas.join(', ')}\n`;
    }
    if (tema.notas) {
      info += `  - Notas: ${tema.notas}\n`;
    }
    info += '\n';
  });

  return info;
}
