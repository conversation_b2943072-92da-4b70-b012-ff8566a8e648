import { supabase } from '../lib/supabase/supabaseClient';
import { obtenerUsuarioActual } from '../lib/supabase/authService';

/**
 * Función de diagnóstico para verificar colecciones y flashcards en Supabase
 */
export async function diagnosticarColeccionesFlashcards() {
  console.log('=== DIAGNÓSTICO DE COLECCIONES Y FLASHCARDS ===');
  
  try {
    // 1. Verificar usuario actual
    const { user, error: userError } = await obtenerUsuarioActual();
    
    if (userError) {
      console.error('❌ Error al obtener usuario:', userError);
      return;
    }
    
    if (!user) {
      console.error('❌ No hay usuario autenticado');
      return;
    }
    
    console.log('✅ Usuario autenticado:', user.id, user.email);
    
    // 2. Obtener todas las colecciones del usuario
    const { data: colecciones, error: coleccionesError } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('user_id', user.id)
      .order('creado_en', { ascending: false });
    
    if (coleccionesError) {
      console.error('❌ Error al obtener colecciones:', coleccionesError);
      return;
    }
    
    console.log(`\n📚 COLECCIONES ENCONTRADAS: ${colecciones?.length || 0}`);
    
    if (!colecciones || colecciones.length === 0) {
      console.log('⚠️ No se encontraron colecciones para este usuario');
      return;
    }
    
    // 3. Mostrar información de cada colección
    for (const coleccion of colecciones) {
      
      // Verificar flashcards de esta colección
      const { data: flashcards, error: flashcardsError } = await supabase
        .from('flashcards')
        .select('*')
        .eq('coleccion_id', coleccion.id)
        .order('creado_en', { ascending: true });
      
      if (flashcardsError) {
        console.error(`❌ Error al obtener flashcards para "${coleccion.titulo}":`, flashcardsError);
        continue;
      }
      
      // Verificar progreso de flashcards
      if (flashcards && flashcards.length > 0) {
        const { data: progresos, error: progresosError } = await supabase
          .from('progreso_flashcards')
          .select('*')
          .in('flashcard_id', flashcards.map(fc => fc.id));

        if (progresosError) {
          console.error(`❌ Error al obtener progreso para "${coleccion.titulo}":`, progresosError);
        }
      }
    }
    
    // 4. Buscar específicamente "Tema 1 Constitución"
    const coleccionConstitucion = colecciones.find(c =>
      c.titulo.toLowerCase().includes('constitución') ||
      c.titulo.toLowerCase().includes('constitucion') ||
      c.titulo.toLowerCase().includes('tema 1')
    );

    if (coleccionConstitucion) {
      // Verificar flashcards específicamente
      const { data: flashcardsConstitucion, error } = await supabase
        .from('flashcards')
        .select('*')
        .eq('coleccion_id', coleccionConstitucion.id);

      if (error) {
        console.error('❌ Error al obtener flashcards de Constitución:', error);
      }
    }
    
    // 5. Verificar integridad de datos
    // Verificar flashcards huérfanas (sin colección)
    const { data: flashcardsHuerfanas, error: huerfanasError } = await supabase
      .from('flashcards')
      .select('id, coleccion_id, pregunta')
      .not('coleccion_id', 'in', `(${colecciones.map(c => `'${c.id}'`).join(',')})`);

    if (huerfanasError) {
      console.error('❌ Error al verificar flashcards huérfanas:', huerfanasError);
    }
    
  } catch (error) {
    console.error('❌ Error general en diagnóstico:', error);
  }
  

}

/**
 * Función específica para buscar una colección por nombre
 */
export async function buscarColeccionPorNombre(nombre: string) {
  try {
    const { user } = await obtenerUsuarioActual();
    if (!user) return null;

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('user_id', user.id)
      .ilike('titulo', `%${nombre}%`);

    if (error) {
      console.error('Error al buscar colección:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error en búsqueda:', error);
    return null;
  }
}

/**
 * Función específica para diagnosticar la colección "Tema 1 constitución"
 */
export async function diagnosticarColeccionConstitucion() {
  try {
    const { user } = await obtenerUsuarioActual();
    if (!user) {
      console.error('❌ No hay usuario autenticado');
      return;
    }

    // ID específico de la colección según el diagnóstico anterior
    const coleccionId = 'c25c593e-8960-47e3-9db6-c5b7849d4553';

    // 1. Verificar la colección
    const { error: coleccionError } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('id', coleccionId)
      .eq('user_id', user.id)
      .single();

    if (coleccionError) {
      console.error('❌ Error al obtener colección:', coleccionError);
      return;
    }

    // 2. Obtener TODAS las flashcards directamente
    const { data: todasFlashcards, error: flashcardsError } = await supabase
      .from('flashcards')
      .select('*')
      .eq('coleccion_id', coleccionId)
      .order('creado_en', { ascending: true });

    if (flashcardsError) {
      console.error('❌ Error al obtener flashcards:', flashcardsError);
      return;
    }

    // 3. Obtener progreso de todas las flashcards
    const { data: todosProgresos, error: progresosError } = await supabase
      .from('progreso_flashcards')
      .select('*')
      .in('flashcard_id', todasFlashcards?.map(fc => fc.id) || []);

    if (progresosError) {
      console.error('❌ Error al obtener progresos:', progresosError);
    }

    // 4. Simular la función obtenerFlashcardsParaEstudiar
    const ahora = new Date();
    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());

    const flashcardsConProgreso = todasFlashcards?.map(flashcard => {
      const progreso = todosProgresos?.find(p => p.flashcard_id === flashcard.id);

      if (!progreso) {
        return {
          ...flashcard,
          debeEstudiar: true,
          progreso: null
        };
      }

      const proximaRevision = new Date(progreso.proxima_revision);
      const proximaRevisionSinHora = new Date(
        proximaRevision.getFullYear(),
        proximaRevision.getMonth(),
        proximaRevision.getDate()
      );
      const debeEstudiar = proximaRevisionSinHora <= hoy;

      return {
        ...flashcard,
        debeEstudiar,
        progreso: {
          factor_facilidad: progreso.factor_facilidad,
          intervalo: progreso.intervalo,
          repeticiones: progreso.repeticiones,
          estado: progreso.estado,
          proxima_revision: progreso.proxima_revision,
        },
      };
    }) || [];

  } catch (error) {
    console.error('❌ Error en diagnóstico específico:', error);
  }


}
