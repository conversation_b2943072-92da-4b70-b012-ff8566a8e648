// src/app/landing/page.tsx
import React from 'react';
import { PLANS } from '@/lib/stripe/config';
import PlanCard from '@/components/ui/PlanCard';

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-blue-600">OposiAI</h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="#pricing" className="text-gray-500 hover:text-gray-900">
                Precios
              </a>
              <a href="/login" className="text-blue-600 hover:text-blue-500">
                Iniciar <PERSON><PERSON>
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            OposiAI: Tu Preparador Personal Inteligente
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Transforma tu estudio para oposiciones con el poder de la IA. 
            Crea planes de estudio, mapas mentales, tests y flashcards de forma automática y eficiente.
          </p>
          <a
            href="#pricing"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Ver Planes de Suscripción
          </a>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Elige el Plan Perfecto para Ti
            </h2>
            <p className="text-lg text-gray-600">
              Tenemos opciones para cada tipo de opositor. Comienza gratis o desbloquea todo el potencial de OposiAI.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <PlanCard
              id={PLANS.free.id}
              name={PLANS.free.name}
              price={PLANS.free.price}
              features={PLANS.free.features}
            />
            <PlanCard
              id={PLANS.usuario.id}
              name={PLANS.usuario.name}
              price={PLANS.usuario.price}
              features={PLANS.usuario.features}
              isPopular={true}
            />
            <PlanCard
              id={PLANS.pro.id}
              name={PLANS.pro.name}
              price={PLANS.pro.price}
              features={PLANS.pro.features}
            />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              ¿Cómo Funciona OposiAI?
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Elige tu Temario</h3>
              <p className="text-gray-600">
                Selecciona un temario predeterminado o sube tus documentos.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Selecciona el Tema</h3>
              <p className="text-gray-600">
                Escoge el tema específico que quieres preparar.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">3</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Planifica tu Estudio</h3>
              <p className="text-gray-600">
                Deja que la IA diseñe un plan personalizado para ti.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">4</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Empieza a Estudiar</h3>
              <p className="text-gray-600">
                Usa mapas mentales, tests y flashcards generados por IA.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            ¿Listo para Empezar?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Únete a OposiAI y lleva tu preparación al siguiente nivel.
          </p>
          <a
            href="/login"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50"
          >
            Iniciar Sesión
          </a>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p>&copy; 2024 OposiAI. Todos los derechos reservados.</p>
        </div>
      </footer>
    </div>
  );
}
