// src/app/thank-you/page.tsx
'use client';

import React, { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { getPlanById } from '@/lib/stripe/config';
import Link from 'next/link';

export default function ThankYouPage() {
  const searchParams = useSearchParams();
  const planId = searchParams.get('plan') || 'free';
  const sessionId = searchParams.get('session_id');
  
  const plan = getPlanById(planId);

  useEffect(() => {
    // Aquí puedes agregar lógica adicional como:
    // - Tracking de conversión
    // - Actualización de estado en la base de datos
    // - Envío de emails adicionales
  }, [planId, sessionId]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <svg
                className="h-6 w-6 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              ¡Gracias por tu solicitud!
            </h2>
            
            <p className="mt-2 text-center text-sm text-gray-600">
              {plan?.name} - {plan?.price === 0 ? 'Gratis' : `€${(plan?.price || 0) / 100}`}
            </p>
          </div>

          <div className="mt-6">
            <div className="rounded-md bg-blue-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-blue-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Próximos pasos
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      Hemos recibido tu solicitud para el {plan?.name}. 
                      {planId === 'free' 
                        ? ' Te contactaremos pronto para activar tu cuenta gratuita.'
                        : sessionId 
                          ? ' Tu pago ha sido procesado exitosamente. Te contactaremos para activar tu cuenta.'
                          : ' Te contactaremos para completar el proceso de activación.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {sessionId && (
            <div className="mt-4">
              <div className="rounded-md bg-green-50 p-4">
                <div className="text-sm text-green-700">
                  <p><strong>ID de sesión:</strong> {sessionId}</p>
                  <p className="mt-1">Guarda este ID para futuras referencias.</p>
                </div>
              </div>
            </div>
          )}

          <div className="mt-6">
            <Link
              href="/"
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Volver al inicio
            </Link>
          </div>

          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              Si tienes alguna pregunta, contacta con nosotros en{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-500">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
