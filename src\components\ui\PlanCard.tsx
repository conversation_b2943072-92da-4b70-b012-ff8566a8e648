// src/components/ui/PlanCard.tsx
'use client';

import React from 'react';
import Link from 'next/link';

interface PlanCardProps {
  id: string;
  name: string;
  price: number;
  features: string[];
  isPopular?: boolean;
}

export default function PlanCard({ id, name, price, features, isPopular = false }: PlanCardProps) {
  const formatPrice = (price: number) => {
    if (price === 0) return 'Gratis';
    return `€${(price / 100).toFixed(2)}`;
  };

  return (
    <div className={`relative rounded-lg shadow-lg ${isPopular ? 'ring-2 ring-blue-500' : ''}`}>
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-blue-500 text-white px-3 py-1 text-sm font-medium rounded-full">
            Más Popular
          </span>
        </div>
      )}
      
      <div className="bg-white rounded-lg p-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900">{name}</h3>
          <div className="mt-4">
            <span className="text-3xl font-bold text-gray-900">
              {formatPrice(price)}
            </span>
            {id === 'pro' && <span className="text-gray-500">/mes</span>}
          </div>
        </div>

        <ul className="mt-6 space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <svg
                className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-sm text-gray-600">{feature}</span>
            </li>
          ))}
        </ul>

        <div className="mt-8">
          <Link
            href={`/payment?plan=${id}`}
            className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              isPopular
                ? 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                : 'text-blue-600 bg-white border-blue-600 hover:bg-blue-50 focus:ring-blue-500'
            }`}
          >
            {id === 'free' ? 'Empezar Gratis' : `Seleccionar ${name}`}
          </Link>
        </div>
      </div>
    </div>
  );
}
